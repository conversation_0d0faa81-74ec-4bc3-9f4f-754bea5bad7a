<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="360_gallery" xml:space="preserve">
    <value>360 Derece Galeri</value>
  </data>
  <data name="about-nasa" xml:space="preserve">
    <value>Her zaman</value>
  </data>
  <data name="about-nasa2" xml:space="preserve">
    <value>en iyisi için çabalıyoruz</value>
  </data>
  <data name="about_slogan" xml:space="preserve">
    <value>&lt;span&gt;Hayallerinizi tasarımlarla birlikte çiziyoruz&lt;/span&gt;Yaratıcılık ve modernite arasında</value>
  </data>
  <data name="address" xml:space="preserve">
    <value>Adres</value>
  </data>
  <data name="address_title" xml:space="preserve">
    <value>Adres</value>
  </data>
  <data name="all_projects" xml:space="preserve">
    <value>Tüm Projeler</value>
  </data>
  <data name="all_references" xml:space="preserve">
    <value>Tüm Müşteriler</value>
  </data>
  <data name="apply_now" xml:space="preserve">
    <value>Hemen Başvurun</value>
  </data>
  <data name="awards_received" xml:space="preserve">
    <value>Kazandığımız Ödüller</value>
  </data>
  <data name="before_after" xml:space="preserve">
    <value>Önce Sonra</value>
  </data>
  <data name="blog" xml:space="preserve">
    <value>Blog</value>
  </data>
  <data name="career_text" xml:space="preserve">
    <value>SMD Group, başarılı çalışanların şirketlerin başarısının anahtarı olduğuna inanır. Bu nedenle, öne çıkan yetenekleri çekmeye büyük önem veririz. SMD Group olarak, iş gücümüzü geliştirmeye ve şirketimizi en üst seviyeye taşımaya odaklanıyoruz.

Her zaman gelecek vaat eden yetenekleri keşfetmeyi ve hedeflerimizle uyumlu olarak yeteneklerini geliştirmeyi amaçlıyoruz. Ayrıca deneyimli ve uzmanlık sahibi kişilerle ekibimizi güçlendirmeyi hedefliyoruz, çünkü onlar işimize yeni ve faydalı katkılar sağlıyorlar.

Eğer işimizin alanında deneyime sahipseniz, yüksek hedeflere sahip, rekabetçi ve meydan okumaya hazır bir ruha sahipseniz, SMD Group ekibinin bir parçası olabilirsiniz. Lütfen iş başvuru formunu doldurun ve deneyiminize ve becerilerinize uygun bir pozisyon seçin. Ayrıca, açık pozisyonlarımıza başvuru yaparken başvuru şartlarını gözden geçirmenizi rica ederiz.

İstihdam vizyonumuz, bir ekip olarak çalışmayı, çalışanların haklarına saygı göstermeyi ve insan hakları ve etik standartlarına uymayı içerir. Başarılı bir çalışan, müşterilerimize en iyi hizmeti sunmak için nezaket, etik değerler ve yüksek değerlere sahip olmalıdır. Biz, önde gelen yetenekleri ve deneyime sahip uzmanları çekmeye önem veririz ve itibarımızı ve özel hizmetlerimizi korumak ve geliştirmek için her zaman en iyi kişilerle çalışmayı hedefleriz.

SMD Grubu'na katılın ve mutfak tasarımı ve iç dekor konusunda en iyi hedeflere ulaşma yolculuğumuzun bir parçası olun, çünkü biz üst düzey kişilerle çalışmayı hedefliyoruz.</value>
  </data>
  <data name="career_title" xml:space="preserve">
    <value>&lt;h4&gt;Bir iş başvurusunda bulunmak için lütfen &lt;span&gt;alanı&lt;/span&gt; doldurun&lt;/h4&gt;</value>
  </data>
  <data name="click_to_view" xml:space="preserve">
    <value>Dokümanları PDF Olarak Görüntülemek İçin Tıklayınız</value>
  </data>
  <data name="company_name" xml:space="preserve">
    <value>SMD DECORATION</value>
  </data>
  <data name="contact_form_text" xml:space="preserve">
    <value>SMD GROUP ekibimiz gün boyunca sizinle birlikte. Tasarımdan anahtar teslimine kadar sizinle birlikteyiz. Türkiye'nin en iyi dekorasyon şirketlerinden biri olmaktan gurur duyuyoruz. Ekibimiz iç tasarım ve dekorasyon alanında uzman ve profesyoneldir. Evler, daireler, villalar, ofisler, ticari mağazalar ve diğer büyük projeler için iç dekorasyon projelerinin tasarlanması ve uygulanması konusunda hizmetlerimizi sunuyoruz. Projedeki detaylara önem veriyor ve müşterinin vizyonunu en iyi şekilde gerçekleştirmeyi amaçlıyoruz. İhtiyaçlarınıza ve kişisel zevkinize bağlı olarak, benzersiz stilinizi yansıtan ve işlevsel gereksinimlerinizi karşılayan yenilikçi ve çağdaş tasarımlar sunuyoruz. Farklı bir mutfak tasarımı veya eviniz için kapsamlı bir iç tasarım arıyorsanız, bunu sizin için gerçekleştirebiliriz. WhatsApp aracılığıyla veya aşağıdaki formu doldurarak bizimle iletişime geçmekten çekinmeyin. Fikirlerinizi somut bir gerçeğe dönüştürmede size yardımcı olmaktan mutluluk duyarız.</value>
  </data>
  <data name="contact_form_title" xml:space="preserve">
    <value />
  </data>
  <data name="contact_text" xml:space="preserve">
    <value>SMD GROUP ekibi günün her saati yanınızda, tasarımdan anahtar teslime kadar yanınızdayız.</value>
  </data>
  <data name="contact_title" xml:space="preserve">
    <value>Bazı örnekleri şu adreste bulabilirsiniz:</value>
  </data>
  
  <data name="date" xml:space="preserve">
    <value>Tarih</value>
  </data>
  <data name="date_of_birth" xml:space="preserve">
    <value>Doğum Tarihi</value>
  </data>
  <data name="email" xml:space="preserve">
    <value>E-posta Adresi</value>
  </data>
  <data name="female" xml:space="preserve">
    <value>Kadın</value>
  </data>
  <data name="founder_name" xml:space="preserve">
    <value>Ad Soyad</value>
  </data>
  <data name="g360_gallery_title" xml:space="preserve">
    <value>360 Derece Galeri</value>
  </data>
  <data name="gender" xml:space="preserve">
    <value>Cinsiyetini seç</value>
  </data>
  <data name="get_offer" xml:space="preserve">
    <value>Teklif Al</value>
  </data>
  <data name="get_offer_btn" xml:space="preserve">
    <value>Teklif Al</value>
  </data>
  <data name="get_offer_text" xml:space="preserve">
    <value>Adımınızı atın ve mühendislik dünyasının uzmanlarıyla iletişime geçerek size en uygun fiyatları sunalım.</value>
  </data>
  <data name="go_back" xml:space="preserve">
    <value>Geri</value>
  </data>
  <data name="home" xml:space="preserve">
    <value>Anasayfa</value>
  </data>
  <data name="home_our_projects" xml:space="preserve">
    <value>Projelerimiz</value>
  </data>
  <data name="home_ref_title" xml:space="preserve">
    <value />
  </data>
  <data name="home_slogan" xml:space="preserve">
    <value>Lüks konut ve ticari projeler, dekorasyon, iç mimari ve dış cephe kaplama konusunda uzmanlaşmış bir şirketiz. Türkiye'deki dekorasyon firmalarının zirvesinde yer almak için Türkiye'nin birçok ilinde harika projelere imza attık. Tasarımlarımız profesyoneldir ve müşterilerimizin isteklerini karşılama konusunda tutkuludur.
                         &lt;br&gt;&lt;br&gt;
                         Tasarımda doğruluk, uygulama ve denetimde kaliteyi garanti eden yüksek yeteneklere sahip teknik ve teknik kadrolar kullanıyoruz.</value>
  </data>
  <data name="how_can_we_get" xml:space="preserve">
    <value>Nasıl Geri Dönüş Yapalım</value>
  </data>
  <data name="how_email" xml:space="preserve">
    <value>E-posta Adresi</value>
  </data>
  <data name="how_phone" xml:space="preserve">
    <value>Telefon Araması</value>
  </data>
  <data name="how_sms" xml:space="preserve">
    <value>Kısa Mesaj</value>
  </data>
  <data name="istanbul_vd" xml:space="preserve">
    <value>İstanbul.VD.</value>
  </data>
  <data name="istanbul_vd_number" xml:space="preserve">
    <value>00330044</value>
  </data>
  <data name="job" xml:space="preserve">
    <value>Mesleğinizi Seçiniz</value>
  </data>
  <data name="logo_alt" xml:space="preserve">
    <value>SMD | SMD Decoration Architecture</value>
  </data>
  <data name="mail_and_fax" xml:space="preserve">
    <value>Mail ve Fax</value>
  </data>
  <data name="male" xml:space="preserve">
    <value>Erkek</value>
  </data>
  <data name="message" xml:space="preserve">
    <value>Mesaj</value>
  </data>
  <data name="messages" xml:space="preserve">
    <value>Mesajlar</value>
  </data>
  <data name="name" xml:space="preserve">
    <value>Ad</value>
  </data>
  <data name="other_info" xml:space="preserve">
    <value>Diğer Bilgiler</value>
  </data>
  <data name="our_documents" xml:space="preserve">
    <value>Dokümanlarımız</value>
  </data>
  <data name="our_founder" xml:space="preserve">
    <value>Kurucumuz</value>
  </data>
  <data name="our_founder_desc" xml:space="preserve">
    <value>Türkiye'de iç mimari, smd dekorasyon alanında ilk ve lider firma olan SMD Grup, Müh. 2012 yılında Suriye ve biz bir smd dekorasyon ailesi olarak başarımızın ve başarısının tüm müşterilerimize fayda sağladığına inanıyoruz.</value>
  </data>
  <data name="our_founder_text" xml:space="preserve">
    <value>M Mervan Anis</value>
  </data>
  <data name="our_projects" xml:space="preserve">
    <value>Projelerimiz</value>
  </data>
  <data name="our_projects_text" xml:space="preserve">
    <value>&lt;span&gt; çalışmalarımızdan bazı örnekleri galerimizi inceleyerek bulabilirsiniz. &lt;/span&gt;</value>
  </data>
  <data name="our_services" xml:space="preserve">
    <value>Hizmetlerimiz</value>
  </data>
  <data name="our_services_text" xml:space="preserve">
    <value>2D ve 3D tasarım, mağaza çizimleri ve miktar hesaplamaları
Her türlü iç ve dış projenin denetimi ve uygulanması
Her türlü mühendislik modellerinin imalatı</value>
  </data>
  <data name="out_founder_text" xml:space="preserve">
    <value>&lt;span&gt; çalışmalarımızdan bazı örnekleri galerimizi inceleyerek bulabilirsiniz. &lt;/span&gt;</value>
  </data>
  <data name="photo_gallery" xml:space="preserve">
    <value>SMD Galeri</value>
  </data>
  <data name="photo_gallery_title" xml:space="preserve">
    <value />
  </data>
  <data name="project_map_text" xml:space="preserve">
    <value>SMD GROUP Türkiye'den dünyanın tüm ülkelerine ve Türkiye'nin tüm bölgelerinde faaliyet göstermektedir SMD GROUP her yerde</value>
  </data>
  <data name="project_map_title" xml:space="preserve">
    <value>Yaptığımız işlerden bazı örnekleri burada bulabilirsiniz.</value>
  </data>
  <data name="project_see_more" xml:space="preserve">
    <value>Proje Detayını İncelemek İçin Tıklayınız</value>
  </data>
  <data name="project_video" xml:space="preserve">
    <value>Proje Videosu</value>
  </data>
  <data name="projects_as_countries" xml:space="preserve">
    <value>Ülkeler Ve Projeler</value>
  </data>
  <data name="projects_countries" xml:space="preserve">
    <value>Ülkeler Ve Projeler</value>
  </data>
  <data name="projects_countries_text" xml:space="preserve">
    <value>SMD GRUP sadece Türkiye'de değil tüm dünyada</value>
  </data>
  <data name="see_all" xml:space="preserve">
    <value>Hepsini Gör</value>
  </data>
  <data name="see_countries" xml:space="preserve">
    <value>Ülkelere Göz At</value>
  </data>
  <data name="see_projects" xml:space="preserve">
    <value>Projelere Göz At</value>
  </data>
  <data name="social_media" xml:space="preserve">
    <value>Sosyal Medya</value>
  </data>
  <data name="starting_date" xml:space="preserve">
    <value>Başlangıç Tarihi</value>
  </data>
  <data name="submit_the_form" xml:space="preserve">
    <value>Formu Gönder</value>
  </data>
  <data name="surname" xml:space="preserve">
    <value>Soyad</value>
  </data>
  <data name="tel" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="telephone" xml:space="preserve">
    <value>Telefon</value>
  </data>
  <data name="video_gallery" xml:space="preserve">
    <value>Video Galeri</value>
  </data>
  <data name="video_gallery_title" xml:space="preserve">
    <value>360 Derece Galeri</value>
  </data>
  <data name="view_all" xml:space="preserve">
    <value>Tümünü Gör</value>
  </data>
  <data name="view_awards" xml:space="preserve">
    <value>Ödüllere Göz Atın</value>
  </data>
  <data name="view_details" xml:space="preserve">
    <value>Detayları Görüntüle</value>
  </data>
  <data name="view_document" xml:space="preserve">
    <value>Dokümanları Görüntüle</value>
  </data>
  <data name="vision_mission" xml:space="preserve">
    <value>Vizyonumuz Ve Misyonumuz</value>
  </data>
  <data name="vision_mission_text" xml:space="preserve">
    <value>Vizyonumuz &lt;br&gt; Evinizde, ofisinizde, iş yerinizde veya yatırımınızda, nerede olursanız olun güzellik ve incelik yaratmayı hedefliyoruz.Gayrimenkul projesi fikrinden başlayıp, uygulama aşamalarından geçerek pazarlamaya kadar. iç mimari ve dekorasyonun yanı sıra proje satış ve satışı... Bizim için sadece verdiğimiz bir hizmet değil, yarattığımız bir yaşam tarzı... Sizi modernliğin kalbine yerleştiren tasarımlar. Pratik veya estetik Tasarımda mükemmellik ve uygulamadaki başarısı, kalite ve araştırma yaklaşımımız için temeldir.Her proje, müşterinin vizyonuna değer vermek ve beklentilerini aşmak için eşsiz bir fırsattır &lt;br&gt; Hedefimiz &lt;br&gt; Hedefimiz ve hedefimiz müşterilerimiz birdir ve hepimiz tek elden çalışarak istenilen hayale ayrılan bütçeler, yüksek kalite ve ses getiren sınırlar içinde ulaşmak için çalışırız.</value>
  </data>
  <data name="we_always_wordk" xml:space="preserve">
    <value>&lt;span&gt;Çabalıyoruz&lt;/span&gt; &lt;i class="fi flaticon-quality-1"&gt;&lt;/i&gt; her zaman en iyisi için</value>
  </data>
  <data name="whyus" xml:space="preserve">
    <value>Neden Biz</value>
  </data>
  <data name="whyus_text" xml:space="preserve">
    <value>&lt;p style="text-align:right;"&gt;Aşağıdaki alanlarda çalışan Türk ve Avrupa şirketlerini ve fabrikalarını temsil ediyoruz:&lt;/p&gt;&lt;p style="text-align:right;"&gt;&amp;nbsp;&lt;/p&gt; &lt;p style="text-align:right;"&gt;- Otel ve restoran ekipman ve malzemeleri&lt;/p&gt;&lt;p style="text-align: right;"&gt;- Her türlü doğal ve suni taş, mermer, seramik ve parke&lt;/p&gt;&lt;p style="text-align:right;"&gt;- Her türlü dış ve iç yangın söndürme kapı ve pencerelerinin vb. emniyete alınması&lt;/p&gt;&lt;p style="text-align:right; "&gt;- Sıhhi tesisat, banyo ve mutfak ekipmanları ve aksesuarları&lt;/p&gt;&lt;p p&gt;&lt;p style="text-align:right;"&gt;- Her türlü ofis, ev ve mutfak mobilyası&lt;/p&gt;&lt;p style= "text-align:right;"&gt;- Elektrikli aksesuarlar&lt;/p&gt;&lt;p style=" text-align:right;"&gt;- Elektrikli mobil tavan armatürleri&lt;/p&gt;</value>
  </data>
  <data name="year_winner" xml:space="preserve">
    <value>Yılın Kazananı:</value>
  </data>
  <data name="about_us" xml:space="preserve">
    <value>Hakkımızda</value>
  </data>
    <data name="contactus" xml:space="preserve">
    <value>Bize Ulaşın</value>
  </data>
  <data name="gallary" xml:space="preserve">
    <value>Galeri</value>
  </data>
  <data name="ourblog" xml:space="preserve">
    <value>haberlerimiz</value>
  </data>
  <data name="our_work" xml:space="preserve">
    <value>Müşteriler</value>
  </data>
  <data name="whysmd" xml:space="preserve">
    <value>Neden SMD grubu</value>
  </data>
  <data name="coproject" xml:space="preserve">
    <value>Ticari Projeler</value>
  </data>
  <data name="hr" xml:space="preserve">
    <value>Beşeriyet Kaynakları</value>
  </data>
  <data name="project" xml:space="preserve">
    <value>projeler</value>
  </data>
  <data name="resproject" xml:space="preserve">
    <value>Konut projeleri</value>
  </data>
  <data name="video" xml:space="preserve">
    <value>video</value>
  </data>
  <data name="from" xml:space="preserve">
    <value>itibaren</value>
  </data>
  <data name="proj23" xml:space="preserve">
    <value>Sayfa</value>
    <comment>مشاريعنا</comment>
  </data>
  <data name="proj24" xml:space="preserve">
    <value>itibaren</value>
    <comment>مشاريعنا</comment>
  </data>
  <data name="qslider" xml:space="preserve">
    <value>Sorularınız ve iletişim için:</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value>360 ​​derece döndürür</value>
  </data>
  <data name="String265" xml:space="preserve">
    <value>Listeye dön</value>
  </data>
  <data name="String302" xml:space="preserve">
    <value>silme işleminden emin misiniz?</value>
  </data>
  <data name="String308" xml:space="preserve">
    <value>Öğeyi sil</value>
  </data>
  <data name="String317" xml:space="preserve">
    <value>Öğe özellikleri</value>
  </data>
  <data name="String330" xml:space="preserve">
    <value>Oluşturuldu</value>
  </data>
  <data name="String331" xml:space="preserve">
    <value>Değiştirilme tarihi</value>
  </data>
  <data name="String10" xml:space="preserve">
    <value>Ayrıntıları</value>
  </data>
  <data name="String11" xml:space="preserve">
    <value>silme</value>
  </data>
  <data name="String12" xml:space="preserve">
    <value>Bir öğe ekle</value>
  </data>
  <data name="String13" xml:space="preserve">
    <value>Değiştirme Maddesi</value>
  </data>
  <data name="String2" xml:space="preserve">
    <value>ekle</value>
  </data>
  <data name="String3" xml:space="preserve">
    <value>Ödüllerin yönetimi</value>
  </data>
  <data name="String4" xml:space="preserve">
    <value>adresi</value>
  </data>
  <data name="String5" xml:space="preserve">
    <value>yıl</value>
  </data>
  <data name="String6" xml:space="preserve">
    <value>İlk sayfada göster</value>
  </data>
  <data name="String7" xml:space="preserve">
    <value>resmi</value>
  </data>
  <data name="String8" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="String9" xml:space="preserve">
    <value>Değiştir</value>
  </data>
  <data name="String14" xml:space="preserve">
    <value>Dili:</value>
  </data>
  <data name="String15" xml:space="preserve">
    <value>lütfen kontrol edin</value>
  </data>
  <data name="String16" xml:space="preserve">
    <value>Bağlantısı</value>
  </data>
  <data name="String17" xml:space="preserve">
    <value>Arap bağlantısı</value>
  </data>
  <data name="String18" xml:space="preserve">
    <value>içeriği</value>
  </data>
  <data name="String19" xml:space="preserve">
    <value>resim yükleme</value>
  </data>
  <data name="String20" xml:space="preserve">
    <value>dosyayı yükleyin</value>
  </data>
  <data name="String21" xml:space="preserve">
    <value>sayfası</value>
  </data>
  <data name="String22" xml:space="preserve">
    <value>Block Management</value>
  </data>
  <data name="String23" xml:space="preserve">
    <value>Edit Block </value>
  </data>
  <data name="String31" xml:space="preserve">
    <value>Add Award</value>
  </data>
  <data name="String32" xml:space="preserve">
    <value>Modify Award</value>
  </data>
  <data name="thanks" xml:space="preserve">
    <value>teşekkürler</value>
    <comment>شكرا</comment>
  </data>
  <data name="thanks1" xml:space="preserve">
    <value>Talebiniz alındı ​​ve takip için sizinle iletişime geçilecektir.</value>
    <comment>شكرا</comment>
  </data>
  <data name="String24" xml:space="preserve">
    <value>Alan zorunludur</value>
  </data>
  <data name="String25" xml:space="preserve">
    <value>Lütfen e-postayı doğru yazın</value>
  </data>
  <data name="String26" xml:space="preserve">
    <value>Geçerli bir telefon numarası giriniz</value>
  </data>
  <data name="String27" xml:space="preserve">
    <value>Mesleğinizi seçin</value>
  </data>
  <data name="String28" xml:space="preserve">
    <value>Mesleğinizi seçin</value>
  </data>
  <data name="String29" xml:space="preserve">
    <value>Video Düzenleyici</value>
  </data>
  <data name="String30" xml:space="preserve">
    <value>muhasebecisi</value>
  </data>
  <data name="String33" xml:space="preserve">
    <value>İçerik Yazıcısı</value>
  </data>
  <data name="String34" xml:space="preserve">
    <value>tasarımcısı</value>
  </data>
  <data name="String35" xml:space="preserve">
    <value>programcısı</value>
  </data>
  <data name="String36" xml:space="preserve">
    <value>Grafik Tasarımcısı</value>
  </data>
  <data name="String37" xml:space="preserve">
    <value>Mimarı</value>
  </data>
  <data name="String38" xml:space="preserve">
    <value>koordinatörü</value>
  </data>
  <data name="String39" xml:space="preserve">
    <value>Proje Yöneticisi</value>
  </data>
  <data name="String40" xml:space="preserve">
    <value>adresi</value>
  </data>
  <data name="String41" xml:space="preserve">
    <value>adresi</value>
  </data>
  <data name="String42" xml:space="preserve">
    <value>Slider</value>
  </data>
  <data name="String43" xml:space="preserve">
    <value>en çok ziyaret edilen haberler</value>
  </data>
  <data name="String44" xml:space="preserve">
    <value>en çok ziyaret edilen projeler</value>
  </data>
  <data name="String45" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="String46" xml:space="preserve">
    <value>Website</value>
  </data>
  <data name="String47" xml:space="preserve">
    <value>Project Category</value>
  </data>
  <data name="String48" xml:space="preserve">
    <value>Post Category</value>
  </data>
  <data name="String49" xml:space="preserve">
    <value>SEO Page</value>
  </data>
  <data name="String50" xml:space="preserve">
    <value>bir blog ekle</value>
  </data>
  <data name="String51" xml:space="preserve">
    <value>Blogu Değiştir</value>
  </data>
  <data name="String52" xml:space="preserve">
    <value>Bir proje ekle</value>
  </data>
  <data name="String53" xml:space="preserve">
    <value>Bir projeyi değiştir</value>
  </data>
  <data name="String54" xml:space="preserve">
    <value>Various</value>
  </data>
  <data name="String55" xml:space="preserve">
    <value>360 derece ekler</value>
  </data>
  <data name="String56" xml:space="preserve">
    <value>360 derece ayarı</value>
  </data>
  <data name="String57" xml:space="preserve">
    <value>Video ekle</value>
  </data>
  <data name="String58" xml:space="preserve">
    <value>Bir videoyu düzenleyin</value>
  </data>
  <data name="String59" xml:space="preserve">
    <value>Çalışan ekle</value>
  </data>
  <data name="String60" xml:space="preserve">
    <value>Çalışanı değiştir</value>
  </data>
  <data name="String61" xml:space="preserve">
    <value>Kullanıcı Adı</value>
  </data>
  <data name="String62" xml:space="preserve">
    <value>şifresi</value>
  </data>
  <data name="String63" xml:space="preserve">
    <value>geçerliliği</value>
  </data>
  <data name="String64" xml:space="preserve">
    <value>aktif</value>
  </data>
  <data name="String65" xml:space="preserve">
    <value>360 derece</value>
  </data>
  <data name="String66" xml:space="preserve">
    <value>kapak resmi</value>
  </data>
  <data name="String67" xml:space="preserve">
    <value>smd videos</value>
  </data>
  <data name="String68" xml:space="preserve">
    <value>smd videos categories</value>
  </data>
  <data name="String69" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="String70" xml:space="preserve">
    <value>Page Management</value>
  </data>
  <data name="String71" xml:space="preserve">
    <value>Edit Page</value>
  </data>
  <data name="String72" xml:space="preserve">
    <value>Num Of Visit</value>
  </data>
  <data name="String73" xml:space="preserve">
    <value>Posts Categories Management</value>
  </data>
  <data name="String74" xml:space="preserve">
    <value>Create Post Category</value>
  </data>
  <data name="String75" xml:space="preserve">
    <value>Edit Post Category</value>
  </data>
  <data name="String76" xml:space="preserve">
    <value>Details Post Category</value>
  </data>
  <data name="String77" xml:space="preserve">
    <value>Delete Post Category</value>
  </data>
  <data name="String78" xml:space="preserve">
    <value>ProjectsCategories Management</value>
  </data>
  <data name="String79" xml:space="preserve">
    <value>Create Project Category</value>
  </data>
  <data name="String80" xml:space="preserve">
    <value>Edit Project Category</value>
  </data>
  <data name="String81" xml:space="preserve">
    <value>Details Project Category</value>
  </data>
  <data name="String82" xml:space="preserve">
    <value>Delete Project Category</value>
  </data>
  <data name="String83" xml:space="preserve">
    <value>Arrange</value>
  </data>
  <data name="String84" xml:space="preserve">
    <value>360 Degress Management</value>
  </data>
  <data name="String85" xml:space="preserve">
    <value>Videos Management</value>
  </data>
  <data name="String86" xml:space="preserve">
    <value>Post Category</value>
  </data>
  <data name="String87" xml:space="preserve">
    <value>Project Category</value>
  </data>
  <data name="String88" xml:space="preserve">
    <value>Publish</value>
  </data>
  <data name="String89" xml:space="preserve">
    <value>Show Whats app</value>
  </data>
  <data name="String90" xml:space="preserve">
    <value>Show Comments</value>
  </data>
  <data name="String91" xml:space="preserve">
    <value>Auther</value>
  </data>
  <data name="String92" xml:space="preserve">
    <value>Latitude</value>
  </data>
  <data name="String93" xml:space="preserve">
    <value>Longitude</value>
  </data>
  <data name="String94" xml:space="preserve">
    <value>Project Gallary</value>
  </data>
  <data name="String95" xml:space="preserve">
    <value>Create Project Gallary</value>
  </data>
  <data name="String96" xml:space="preserve">
    <value>Post</value>
  </data>
  <data name="String100" xml:space="preserve">
    <value>details of client</value>
  </data>
  <data name="String101" xml:space="preserve">
    <value>delete client</value>
  </data>
  <data name="String102" xml:space="preserve">
    <value>Slider Management</value>
  </data>
  <data name="String103" xml:space="preserve">
    <value>add slider</value>
  </data>
  <data name="String104" xml:space="preserve">
    <value>edit slider</value>
  </data>
  <data name="String105" xml:space="preserve">
    <value>details of slider</value>
  </data>
  <data name="String106" xml:space="preserve">
    <value>delete slider</value>
  </data>
  <data name="String107" xml:space="preserve">
    <value>Video Categories Management</value>
  </data>
  <data name="String108" xml:space="preserve">
    <value>Add Video Category</value>
  </data>
  <data name="String109" xml:space="preserve">
    <value>edit video category</value>
  </data>
  <data name="String110" xml:space="preserve">
    <value>details of video category</value>
  </data>
  <data name="String111" xml:space="preserve">
    <value>delete video category</value>
  </data>
  <data name="String112" xml:space="preserve">
    <value>Smd videos management</value>
  </data>
  <data name="String113" xml:space="preserve">
    <value>add Smd videos</value>
  </data>
  <data name="String114" xml:space="preserve">
    <value>edit Smd videos</value>
  </data>
  <data name="String115" xml:space="preserve">
    <value>details of Smd videos</value>
  </data>
  <data name="String97" xml:space="preserve">
    <value>Client Management</value>
  </data>
  <data name="String98" xml:space="preserve">
    <value>Add client</value>
  </data>
  <data name="String99" xml:space="preserve">
    <value>edit client</value>
  </data>
  <data name="String116" xml:space="preserve">
    <value>delete Smd videos</value>
  </data>
  <data name="String117" xml:space="preserve">
    <value>FAQ</value>
  </data>
  <data name="String118" xml:space="preserve">
    <value>FAQ Management</value>
  </data>
  <data name="String119" xml:space="preserve">
    <value>Add FAQ</value>
  </data>
  <data name="String120" xml:space="preserve">
    <value>Edit FAQ</value>
  </data>
  <data name="String121" xml:space="preserve">
    <value>Details of FAQ</value>
  </data>
  <data name="String122" xml:space="preserve">
    <value>Delete FAQ</value>
  </data>
  <data name="String123" xml:space="preserve">
    <value>Question</value>
  </data>
  <data name="String124" xml:space="preserve">
    <value>Answer</value>
  </data>
  <data name="String125" xml:space="preserve">
    <value>Image Center</value>
  </data>
  <data name="String126" xml:space="preserve">
    <value>Add Group Of Photos</value>
  </data>
  <data name="String127" xml:space="preserve">
    <value>Copy Link</value>
  </data>
  <data name="category" xml:space="preserve">
    <value>Dekorasyonda en önemli bölümler</value>
  </data>
  <data name="important" xml:space="preserve">
    <value>Önemli bağlantılar</value>
  </data>
  <data name="jobs" xml:space="preserve">
    <value>iş imkanı</value>
  </data>
  <data name="nslogan" xml:space="preserve">
    <value>SMD Dekorasyon Suudi Arabistan, Emirlikler, Katar, Libya ve Irak'taki çalışmalarının yanı sıra&lt;span  style=" color: #bf9d7f;"&gt; İstanbul'un en önemli dekorasyon firmasıdır.&lt;/span&gt;</value>
  </data>
  <data name="cv" xml:space="preserve">
    <value>CV</value>
  </data>
  <data name="file" xml:space="preserve">
    <value>Dosya Gönder</value>
  </data>
  <data name="saudi" xml:space="preserve">
    <value>Biz Suudi Arabistan'dayız</value>
  </data>
  <data name="suaditext" xml:space="preserve">
    <value>Dekorasyon ve iç tasarım konusunda yenilikçi fikirler arıyorsanız, doğru yerdesiniz. İhtiyaçlarınıza ve kişisel zevkinize uygun mekanlar tasarlamak için zarafeti ve rahatlığı birleştiren benzersiz çözümler sunuyoruz. Evizi yenilemek veya ofisinizi en son modern dekorasyon tarzlarıyla tasarlamak istiyorsanız, en iyi danışmanlık ve hizmetleri sunmak için buradayız. Suudi Arabistan'daki yeni projelerimizi şimdi takip edin; işimizi genişletmeye ve yeni şubelerimizde uzmanlığımızı sunmaya devam ediyoruz. Fırsatı kaçırmayın, şimdi giriş yapın ve her zaman güncel kalın!</value>
  </data>
  <data name="String128" xml:space="preserve">
    <value>Teklif sitesi</value>
  </data>
  <data name="String129" xml:space="preserve">
    <value>Şirketimizin resmi Suudi Arabistan web sitesi</value>
  </data>
</root>