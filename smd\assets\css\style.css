@charset "UTF-8";
@import url('https://fonts.googleapis.com/css2?family=Alexandria:wght@100;200;300;400;500;600;700;800;900&family=Cairo:wght@200;300;400;500;600;700;800;900;1000&family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,300&display=swap');
@import url("https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css");
@import url("https://cdn.jsdelivr.net/gh/fancyapps/fancybox@3.5.7/dist/jquery.fancybox.min.css");
@import url("https://unpkg.com/aos@2.3.1/dist/aos.css");
@charest "utf-8";

.hamburger {
    cursor: pointer;
    -webkit-transition-property: opacity, -webkit-filter;
    transition-property: opacity, -webkit-filter;
    transition-property: opacity, filter;
    transition-property: opacity, filter, -webkit-filter;
    -webkit-transition-duration: 0.15s;
    transition-duration: 0.15s;
    -webkit-transition-timing-function: linear;
    transition-timing-function: linear;
    font: inherit;
    color: inherit;
    text-transform: none;
    background-color: transparent;
    border: 0;
    margin: 0;
    overflow: visible;
}

    .hamburger:hover {
        opacity: 0.7;
    }

.hamburger-box {
    width: 40px;
    height: 24px;
    display: inline-block;
    position: relative;
}

.hamburger-inner {
    display: block;
    top: 50%;
    margin-top: -2px;
}

    .hamburger-inner, .hamburger-inner::before, .hamburger-inner::after {
        width: 40px;
        height: 4px;
        background-color: #fff;
        border-radius: 4px;
        position: absolute;
        -webkit-transition-property: -webkit-transform;
        transition-property: -webkit-transform;
        transition-property: transform;
        transition-property: transform, -webkit-transform;
        -webkit-transition-duration: 0.15s;
        transition-duration: 0.15s;
        -webkit-transition-timing-function: ease;
        transition-timing-function: ease;
    }

        .hamburger-inner::before, .hamburger-inner::after {
            content: "";
            display: block;
        }

        .hamburger-inner::before {
            top: -10px;
        }

        .hamburger-inner::after {
            bottom: -10px;
        }

.hamburger--3dx .hamburger-box {
    -webkit-perspective: 80px;
    perspective: 80px;
}

.hamburger--3dx .hamburger-inner {
    -webkit-transition: background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
}

    .hamburger--3dx .hamburger-inner::before, .hamburger--3dx .hamburger-inner::after {
        -webkit-transition: -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

.hamburger--3dx.is-active .hamburger-inner {
    background-color: transparent;
    -webkit-transform: rotateY(180deg);
    transform: rotateY(180deg);
}

    .hamburger--3dx.is-active .hamburger-inner::before {
        -webkit-transform: translate3d(0, 10px, 0) rotate(45deg);
        transform: translate3d(0, 10px, 0) rotate(45deg);
    }

    .hamburger--3dx.is-active .hamburger-inner::after {
        -webkit-transform: translate3d(0, -10px, 0) rotate(-45deg);
        transform: translate3d(0, -10px, 0) rotate(-45deg);
    }

.hamburger--3dx-r .hamburger-box {
    -webkit-perspective: 80px;
    perspective: 80px;
}

.hamburger--3dx-r .hamburger-inner {
    -webkit-transition: background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
}

    .hamburger--3dx-r .hamburger-inner::before, .hamburger--3dx-r .hamburger-inner::after {
        -webkit-transition: -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

.hamburger--3dx-r.is-active .hamburger-inner {
    background-color: transparent;
    -webkit-transform: rotateY(-180deg);
    transform: rotateY(-180deg);
}

    .hamburger--3dx-r.is-active .hamburger-inner::before {
        -webkit-transform: translate3d(0, 10px, 0) rotate(45deg);
        transform: translate3d(0, 10px, 0) rotate(45deg);
    }

    .hamburger--3dx-r.is-active .hamburger-inner::after {
        -webkit-transform: translate3d(0, -10px, 0) rotate(-45deg);
        transform: translate3d(0, -10px, 0) rotate(-45deg);
    }

.hamburger--3dy .hamburger-box {
    -webkit-perspective: 80px;
    perspective: 80px;
}

.hamburger--3dy .hamburger-inner {
    -webkit-transition: background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
}

    .hamburger--3dy .hamburger-inner::before, .hamburger--3dy .hamburger-inner::after {
        -webkit-transition: -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

.hamburger--3dy.is-active .hamburger-inner {
    background-color: transparent;
    -webkit-transform: rotateX(-180deg);
    transform: rotateX(-180deg);
}

    .hamburger--3dy.is-active .hamburger-inner::before {
        -webkit-transform: translate3d(0, 10px, 0) rotate(45deg);
        transform: translate3d(0, 10px, 0) rotate(45deg);
    }

    .hamburger--3dy.is-active .hamburger-inner::after {
        -webkit-transform: translate3d(0, -10px, 0) rotate(-45deg);
        transform: translate3d(0, -10px, 0) rotate(-45deg);
    }

.hamburger--3dy-r .hamburger-box {
    -webkit-perspective: 80px;
    perspective: 80px;
}

.hamburger--3dy-r .hamburger-inner {
    -webkit-transition: background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
}

    .hamburger--3dy-r .hamburger-inner::before, .hamburger--3dy-r .hamburger-inner::after {
        -webkit-transition: -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

.hamburger--3dy-r.is-active .hamburger-inner {
    background-color: transparent;
    -webkit-transform: rotateX(180deg);
    transform: rotateX(180deg);
}

    .hamburger--3dy-r.is-active .hamburger-inner::before {
        -webkit-transform: translate3d(0, 10px, 0) rotate(45deg);
        transform: translate3d(0, 10px, 0) rotate(45deg);
    }

    .hamburger--3dy-r.is-active .hamburger-inner::after {
        -webkit-transform: translate3d(0, -10px, 0) rotate(-45deg);
        transform: translate3d(0, -10px, 0) rotate(-45deg);
    }

.hamburger--3dxy .hamburger-box {
    -webkit-perspective: 80px;
    perspective: 80px;
}

.hamburger--3dxy .hamburger-inner {
    -webkit-transition: background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
}

    .hamburger--3dxy .hamburger-inner::before, .hamburger--3dxy .hamburger-inner::after {
        -webkit-transition: -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

.hamburger--3dxy.is-active .hamburger-inner {
    background-color: transparent;
    -webkit-transform: rotateX(180deg) rotateY(180deg);
    transform: rotateX(180deg) rotateY(180deg);
}

    .hamburger--3dxy.is-active .hamburger-inner::before {
        -webkit-transform: translate3d(0, 10px, 0) rotate(45deg);
        transform: translate3d(0, 10px, 0) rotate(45deg);
    }

    .hamburger--3dxy.is-active .hamburger-inner::after {
        -webkit-transform: translate3d(0, -10px, 0) rotate(-45deg);
        transform: translate3d(0, -10px, 0) rotate(-45deg);
    }

.hamburger--3dxy-r .hamburger-box {
    -webkit-perspective: 80px;
    perspective: 80px;
}

.hamburger--3dxy-r .hamburger-inner {
    -webkit-transition: background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
}

    .hamburger--3dxy-r .hamburger-inner::before, .hamburger--3dxy-r .hamburger-inner::after {
        -webkit-transition: -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

.hamburger--3dxy-r.is-active .hamburger-inner {
    background-color: transparent;
    -webkit-transform: rotateX(180deg) rotateY(180deg) rotateZ(-180deg);
    transform: rotateX(180deg) rotateY(180deg) rotateZ(-180deg);
}

    .hamburger--3dxy-r.is-active .hamburger-inner::before {
        -webkit-transform: translate3d(0, 10px, 0) rotate(45deg);
        transform: translate3d(0, 10px, 0) rotate(45deg);
    }

    .hamburger--3dxy-r.is-active .hamburger-inner::after {
        -webkit-transform: translate3d(0, -10px, 0) rotate(-45deg);
        transform: translate3d(0, -10px, 0) rotate(-45deg);
    }

.hamburger--arrow.is-active .hamburger-inner::before {
    -webkit-transform: translate3d(-8px, 0, 0) rotate(-45deg) scale(0.7, 1);
    transform: translate3d(-8px, 0, 0) rotate(-45deg) scale(0.7, 1);
}

.hamburger--arrow.is-active .hamburger-inner::after {
    -webkit-transform: translate3d(-8px, 0, 0) rotate(45deg) scale(0.7, 1);
    transform: translate3d(-8px, 0, 0) rotate(45deg) scale(0.7, 1);
}

.hamburger--arrow-r.is-active .hamburger-inner::before {
    -webkit-transform: translate3d(8px, 0, 0) rotate(45deg) scale(0.7, 1);
    transform: translate3d(8px, 0, 0) rotate(45deg) scale(0.7, 1);
}

.hamburger--arrow-r.is-active .hamburger-inner::after {
    -webkit-transform: translate3d(8px, 0, 0) rotate(-45deg) scale(0.7, 1);
    transform: translate3d(8px, 0, 0) rotate(-45deg) scale(0.7, 1);
}

.hamburger--arrowalt .hamburger-inner::before {
    -webkit-transition: top 0.1s 0.1s ease, -webkit-transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: top 0.1s 0.1s ease, -webkit-transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: top 0.1s 0.1s ease, transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: top 0.1s 0.1s ease, transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1), -webkit-transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.hamburger--arrowalt .hamburger-inner::after {
    -webkit-transition: bottom 0.1s 0.1s ease, -webkit-transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: bottom 0.1s 0.1s ease, -webkit-transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: bottom 0.1s 0.1s ease, transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: bottom 0.1s 0.1s ease, transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1), -webkit-transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.hamburger--arrowalt.is-active .hamburger-inner::before {
    top: 0;
    -webkit-transform: translate3d(-8px, -10px, 0) rotate(-45deg) scale(0.7, 1);
    transform: translate3d(-8px, -10px, 0) rotate(-45deg) scale(0.7, 1);
    -webkit-transition: top 0.1s ease, -webkit-transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
    transition: top 0.1s ease, -webkit-transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
    transition: top 0.1s ease, transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
    transition: top 0.1s ease, transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22), -webkit-transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
}

.hamburger--arrowalt.is-active .hamburger-inner::after {
    bottom: 0;
    -webkit-transform: translate3d(-8px, 10px, 0) rotate(45deg) scale(0.7, 1);
    transform: translate3d(-8px, 10px, 0) rotate(45deg) scale(0.7, 1);
    -webkit-transition: bottom 0.1s ease, -webkit-transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
    transition: bottom 0.1s ease, -webkit-transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
    transition: bottom 0.1s ease, transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
    transition: bottom 0.1s ease, transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22), -webkit-transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
}

.hamburger--arrowalt-r .hamburger-inner::before {
    -webkit-transition: top 0.1s 0.1s ease, -webkit-transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: top 0.1s 0.1s ease, -webkit-transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: top 0.1s 0.1s ease, transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: top 0.1s 0.1s ease, transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1), -webkit-transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.hamburger--arrowalt-r .hamburger-inner::after {
    -webkit-transition: bottom 0.1s 0.1s ease, -webkit-transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: bottom 0.1s 0.1s ease, -webkit-transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: bottom 0.1s 0.1s ease, transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition: bottom 0.1s 0.1s ease, transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1), -webkit-transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.hamburger--arrowalt-r.is-active .hamburger-inner::before {
    top: 0;
    -webkit-transform: translate3d(8px, -10px, 0) rotate(45deg) scale(0.7, 1);
    transform: translate3d(8px, -10px, 0) rotate(45deg) scale(0.7, 1);
    -webkit-transition: top 0.1s ease, -webkit-transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
    transition: top 0.1s ease, -webkit-transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
    transition: top 0.1s ease, transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
    transition: top 0.1s ease, transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22), -webkit-transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
}

.hamburger--arrowalt-r.is-active .hamburger-inner::after {
    bottom: 0;
    -webkit-transform: translate3d(8px, 10px, 0) rotate(-45deg) scale(0.7, 1);
    transform: translate3d(8px, 10px, 0) rotate(-45deg) scale(0.7, 1);
    -webkit-transition: bottom 0.1s ease, -webkit-transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
    transition: bottom 0.1s ease, -webkit-transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
    transition: bottom 0.1s ease, transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
    transition: bottom 0.1s ease, transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22), -webkit-transform 0.1s 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
}

.hamburger--arrowturn.is-active .hamburger-inner {
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg);
}

    .hamburger--arrowturn.is-active .hamburger-inner::before {
        -webkit-transform: translate3d(8px, 0, 0) rotate(45deg) scale(0.7, 1);
        transform: translate3d(8px, 0, 0) rotate(45deg) scale(0.7, 1);
    }

    .hamburger--arrowturn.is-active .hamburger-inner::after {
        -webkit-transform: translate3d(8px, 0, 0) rotate(-45deg) scale(0.7, 1);
        transform: translate3d(8px, 0, 0) rotate(-45deg) scale(0.7, 1);
    }

.hamburger--arrowturn-r.is-active .hamburger-inner {
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg);
}

    .hamburger--arrowturn-r.is-active .hamburger-inner::before {
        -webkit-transform: translate3d(-8px, 0, 0) rotate(-45deg) scale(0.7, 1);
        transform: translate3d(-8px, 0, 0) rotate(-45deg) scale(0.7, 1);
    }

    .hamburger--arrowturn-r.is-active .hamburger-inner::after {
        -webkit-transform: translate3d(-8px, 0, 0) rotate(45deg) scale(0.7, 1);
        transform: translate3d(-8px, 0, 0) rotate(45deg) scale(0.7, 1);
    }

.hamburger--boring .hamburger-inner, .hamburger--boring .hamburger-inner::before, .hamburger--boring .hamburger-inner::after {
    -webkit-transition-property: none;
    transition-property: none;
}

.hamburger--boring.is-active .hamburger-inner {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}

    .hamburger--boring.is-active .hamburger-inner::before {
        top: 0;
        opacity: 0;
    }

    .hamburger--boring.is-active .hamburger-inner::after {
        bottom: 0;
        -webkit-transform: rotate(-90deg);
        transform: rotate(-90deg);
    }

.hamburger--collapse .hamburger-inner {
    top: auto;
    bottom: 0;
    -webkit-transition-duration: 0.13s;
    transition-duration: 0.13s;
    -webkit-transition-delay: 0.13s;
    transition-delay: 0.13s;
    -webkit-transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

    .hamburger--collapse .hamburger-inner::after {
        top: -20px;
        -webkit-transition: top 0.2s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), opacity 0.1s linear;
        transition: top 0.2s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), opacity 0.1s linear;
    }

    .hamburger--collapse .hamburger-inner::before {
        -webkit-transition: top 0.12s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.12s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.12s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.12s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }

.hamburger--collapse.is-active .hamburger-inner {
    -webkit-transform: translate3d(0, -10px, 0) rotate(-45deg);
    transform: translate3d(0, -10px, 0) rotate(-45deg);
    -webkit-transition-delay: 0.22s;
    transition-delay: 0.22s;
    -webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}

    .hamburger--collapse.is-active .hamburger-inner::after {
        top: 0;
        opacity: 0;
        -webkit-transition: top 0.2s cubic-bezier(0.33333, 0, 0.66667, 0.33333), opacity 0.1s 0.22s linear;
        transition: top 0.2s cubic-bezier(0.33333, 0, 0.66667, 0.33333), opacity 0.1s 0.22s linear;
    }

    .hamburger--collapse.is-active .hamburger-inner::before {
        top: 0;
        -webkit-transform: rotate(-90deg);
        transform: rotate(-90deg);
        -webkit-transition: top 0.1s 0.16s cubic-bezier(0.33333, 0, 0.66667, 0.33333), -webkit-transform 0.13s 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.1s 0.16s cubic-bezier(0.33333, 0, 0.66667, 0.33333), -webkit-transform 0.13s 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.1s 0.16s cubic-bezier(0.33333, 0, 0.66667, 0.33333), transform 0.13s 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.1s 0.16s cubic-bezier(0.33333, 0, 0.66667, 0.33333), transform 0.13s 0.25s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.13s 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
    }

.hamburger--collapse-r .hamburger-inner {
    top: auto;
    bottom: 0;
    -webkit-transition-duration: 0.13s;
    transition-duration: 0.13s;
    -webkit-transition-delay: 0.13s;
    transition-delay: 0.13s;
    -webkit-transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

    .hamburger--collapse-r .hamburger-inner::after {
        top: -20px;
        -webkit-transition: top 0.2s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), opacity 0.1s linear;
        transition: top 0.2s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), opacity 0.1s linear;
    }

    .hamburger--collapse-r .hamburger-inner::before {
        -webkit-transition: top 0.12s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.12s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.12s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.12s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }

.hamburger--collapse-r.is-active .hamburger-inner {
    -webkit-transform: translate3d(0, -10px, 0) rotate(45deg);
    transform: translate3d(0, -10px, 0) rotate(45deg);
    -webkit-transition-delay: 0.22s;
    transition-delay: 0.22s;
    -webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}

    .hamburger--collapse-r.is-active .hamburger-inner::after {
        top: 0;
        opacity: 0;
        -webkit-transition: top 0.2s cubic-bezier(0.33333, 0, 0.66667, 0.33333), opacity 0.1s 0.22s linear;
        transition: top 0.2s cubic-bezier(0.33333, 0, 0.66667, 0.33333), opacity 0.1s 0.22s linear;
    }

    .hamburger--collapse-r.is-active .hamburger-inner::before {
        top: 0;
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
        -webkit-transition: top 0.1s 0.16s cubic-bezier(0.33333, 0, 0.66667, 0.33333), -webkit-transform 0.13s 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.1s 0.16s cubic-bezier(0.33333, 0, 0.66667, 0.33333), -webkit-transform 0.13s 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.1s 0.16s cubic-bezier(0.33333, 0, 0.66667, 0.33333), transform 0.13s 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.1s 0.16s cubic-bezier(0.33333, 0, 0.66667, 0.33333), transform 0.13s 0.25s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.13s 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
    }

.hamburger--elastic .hamburger-inner {
    top: 2px;
    -webkit-transition-duration: 0.275s;
    transition-duration: 0.275s;
    -webkit-transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

    .hamburger--elastic .hamburger-inner::before {
        top: 10px;
        -webkit-transition: opacity 0.125s 0.275s ease;
        transition: opacity 0.125s 0.275s ease;
    }

    .hamburger--elastic .hamburger-inner::after {
        top: 20px;
        -webkit-transition: -webkit-transform 0.275s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        transition: -webkit-transform 0.275s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        transition: transform 0.275s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        transition: transform 0.275s cubic-bezier(0.68, -0.55, 0.265, 1.55), -webkit-transform 0.275s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

.hamburger--elastic.is-active .hamburger-inner {
    -webkit-transform: translate3d(0, 10px, 0) rotate(135deg);
    transform: translate3d(0, 10px, 0) rotate(135deg);
    -webkit-transition-delay: 0.075s;
    transition-delay: 0.075s;
}

    .hamburger--elastic.is-active .hamburger-inner::before {
        -webkit-transition-delay: 0s;
        transition-delay: 0s;
        opacity: 0;
    }

    .hamburger--elastic.is-active .hamburger-inner::after {
        -webkit-transform: translate3d(0, -20px, 0) rotate(-270deg);
        transform: translate3d(0, -20px, 0) rotate(-270deg);
        -webkit-transition-delay: 0.075s;
        transition-delay: 0.075s;
    }

.hamburger--elastic-r .hamburger-inner {
    top: 2px;
    -webkit-transition-duration: 0.275s;
    transition-duration: 0.275s;
    -webkit-transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

    .hamburger--elastic-r .hamburger-inner::before {
        top: 10px;
        -webkit-transition: opacity 0.125s 0.275s ease;
        transition: opacity 0.125s 0.275s ease;
    }

    .hamburger--elastic-r .hamburger-inner::after {
        top: 20px;
        -webkit-transition: -webkit-transform 0.275s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        transition: -webkit-transform 0.275s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        transition: transform 0.275s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        transition: transform 0.275s cubic-bezier(0.68, -0.55, 0.265, 1.55), -webkit-transform 0.275s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

.hamburger--elastic-r.is-active .hamburger-inner {
    -webkit-transform: translate3d(0, 10px, 0) rotate(-135deg);
    transform: translate3d(0, 10px, 0) rotate(-135deg);
    -webkit-transition-delay: 0.075s;
    transition-delay: 0.075s;
}

    .hamburger--elastic-r.is-active .hamburger-inner::before {
        -webkit-transition-delay: 0s;
        transition-delay: 0s;
        opacity: 0;
    }

    .hamburger--elastic-r.is-active .hamburger-inner::after {
        -webkit-transform: translate3d(0, -20px, 0) rotate(270deg);
        transform: translate3d(0, -20px, 0) rotate(270deg);
        -webkit-transition-delay: 0.075s;
        transition-delay: 0.075s;
    }

.hamburger--emphatic {
    overflow: hidden;
}

    .hamburger--emphatic .hamburger-inner {
        -webkit-transition: background-color 0.125s 0.175s ease-in;
        transition: background-color 0.125s 0.175s ease-in;
    }

        .hamburger--emphatic .hamburger-inner::before {
            left: 0;
            -webkit-transition: top 0.05s 0.125s linear, left 0.125s 0.175s ease-in, -webkit-transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335);
            transition: top 0.05s 0.125s linear, left 0.125s 0.175s ease-in, -webkit-transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335);
            transition: transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335), top 0.05s 0.125s linear, left 0.125s 0.175s ease-in;
            transition: transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335), top 0.05s 0.125s linear, left 0.125s 0.175s ease-in, -webkit-transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335);
        }

        .hamburger--emphatic .hamburger-inner::after {
            top: 10px;
            right: 0;
            -webkit-transition: top 0.05s 0.125s linear, right 0.125s 0.175s ease-in, -webkit-transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335);
            transition: top 0.05s 0.125s linear, right 0.125s 0.175s ease-in, -webkit-transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335);
            transition: transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335), top 0.05s 0.125s linear, right 0.125s 0.175s ease-in;
            transition: transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335), top 0.05s 0.125s linear, right 0.125s 0.175s ease-in, -webkit-transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335);
        }

    .hamburger--emphatic.is-active .hamburger-inner {
        -webkit-transition-delay: 0s;
        transition-delay: 0s;
        -webkit-transition-timing-function: ease-out;
        transition-timing-function: ease-out;
        background-color: transparent;
    }

        .hamburger--emphatic.is-active .hamburger-inner::before {
            left: -80px;
            top: -80px;
            -webkit-transform: translate3d(80px, 80px, 0) rotate(45deg);
            transform: translate3d(80px, 80px, 0) rotate(45deg);
            -webkit-transition: left 0.125s ease-out, top 0.05s 0.125s linear, -webkit-transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
            transition: left 0.125s ease-out, top 0.05s 0.125s linear, -webkit-transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
            transition: left 0.125s ease-out, top 0.05s 0.125s linear, transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
            transition: left 0.125s ease-out, top 0.05s 0.125s linear, transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1), -webkit-transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
        }

        .hamburger--emphatic.is-active .hamburger-inner::after {
            right: -80px;
            top: -80px;
            -webkit-transform: translate3d(-80px, 80px, 0) rotate(-45deg);
            transform: translate3d(-80px, 80px, 0) rotate(-45deg);
            -webkit-transition: right 0.125s ease-out, top 0.05s 0.125s linear, -webkit-transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
            transition: right 0.125s ease-out, top 0.05s 0.125s linear, -webkit-transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
            transition: right 0.125s ease-out, top 0.05s 0.125s linear, transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
            transition: right 0.125s ease-out, top 0.05s 0.125s linear, transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1), -webkit-transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
        }

.hamburger--emphatic-r {
    overflow: hidden;
}

    .hamburger--emphatic-r .hamburger-inner {
        -webkit-transition: background-color 0.125s 0.175s ease-in;
        transition: background-color 0.125s 0.175s ease-in;
    }

        .hamburger--emphatic-r .hamburger-inner::before {
            left: 0;
            -webkit-transition: top 0.05s 0.125s linear, left 0.125s 0.175s ease-in, -webkit-transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335);
            transition: top 0.05s 0.125s linear, left 0.125s 0.175s ease-in, -webkit-transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335);
            transition: transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335), top 0.05s 0.125s linear, left 0.125s 0.175s ease-in;
            transition: transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335), top 0.05s 0.125s linear, left 0.125s 0.175s ease-in, -webkit-transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335);
        }

        .hamburger--emphatic-r .hamburger-inner::after {
            top: 10px;
            right: 0;
            -webkit-transition: top 0.05s 0.125s linear, right 0.125s 0.175s ease-in, -webkit-transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335);
            transition: top 0.05s 0.125s linear, right 0.125s 0.175s ease-in, -webkit-transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335);
            transition: transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335), top 0.05s 0.125s linear, right 0.125s 0.175s ease-in;
            transition: transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335), top 0.05s 0.125s linear, right 0.125s 0.175s ease-in, -webkit-transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335);
        }

    .hamburger--emphatic-r.is-active .hamburger-inner {
        -webkit-transition-delay: 0s;
        transition-delay: 0s;
        -webkit-transition-timing-function: ease-out;
        transition-timing-function: ease-out;
        background-color: transparent;
    }

        .hamburger--emphatic-r.is-active .hamburger-inner::before {
            left: -80px;
            top: 80px;
            -webkit-transform: translate3d(80px, -80px, 0) rotate(-45deg);
            transform: translate3d(80px, -80px, 0) rotate(-45deg);
            -webkit-transition: left 0.125s ease-out, top 0.05s 0.125s linear, -webkit-transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
            transition: left 0.125s ease-out, top 0.05s 0.125s linear, -webkit-transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
            transition: left 0.125s ease-out, top 0.05s 0.125s linear, transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
            transition: left 0.125s ease-out, top 0.05s 0.125s linear, transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1), -webkit-transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
        }

        .hamburger--emphatic-r.is-active .hamburger-inner::after {
            right: -80px;
            top: 80px;
            -webkit-transform: translate3d(-80px, -80px, 0) rotate(45deg);
            transform: translate3d(-80px, -80px, 0) rotate(45deg);
            -webkit-transition: right 0.125s ease-out, top 0.05s 0.125s linear, -webkit-transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
            transition: right 0.125s ease-out, top 0.05s 0.125s linear, -webkit-transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
            transition: right 0.125s ease-out, top 0.05s 0.125s linear, transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
            transition: right 0.125s ease-out, top 0.05s 0.125s linear, transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1), -webkit-transform 0.125s 0.175s cubic-bezier(0.075, 0.82, 0.165, 1);
        }

.hamburger--minus .hamburger-inner::before, .hamburger--minus .hamburger-inner::after {
    -webkit-transition: bottom 0.08s 0s ease-out, top 0.08s 0s ease-out, opacity 0s linear;
    transition: bottom 0.08s 0s ease-out, top 0.08s 0s ease-out, opacity 0s linear;
}

.hamburger--minus.is-active .hamburger-inner::before, .hamburger--minus.is-active .hamburger-inner::after {
    opacity: 0;
    -webkit-transition: bottom 0.08s ease-out, top 0.08s ease-out, opacity 0s 0.08s linear;
    transition: bottom 0.08s ease-out, top 0.08s ease-out, opacity 0s 0.08s linear;
}

.hamburger--minus.is-active .hamburger-inner::before {
    top: 0;
}

.hamburger--minus.is-active .hamburger-inner::after {
    bottom: 0;
}

.hamburger--slider .hamburger-inner {
    top: 2px;
}

    .hamburger--slider .hamburger-inner::before {
        top: 10px;
        -webkit-transition-property: opacity, -webkit-transform;
        transition-property: opacity, -webkit-transform;
        transition-property: transform, opacity;
        transition-property: transform, opacity, -webkit-transform;
        -webkit-transition-timing-function: ease;
        transition-timing-function: ease;
        -webkit-transition-duration: 0.15s;
        transition-duration: 0.15s;
    }

    .hamburger--slider .hamburger-inner::after {
        top: 20px;
    }

.hamburger--slider.is-active .hamburger-inner {
    -webkit-transform: translate3d(0, 10px, 0) rotate(45deg);
    transform: translate3d(0, 10px, 0) rotate(45deg);
}

    .hamburger--slider.is-active .hamburger-inner::before {
        -webkit-transform: rotate(-45deg) translate3d(-5.71429px, -6px, 0);
        transform: rotate(-45deg) translate3d(-5.71429px, -6px, 0);
        opacity: 0;
    }

    .hamburger--slider.is-active .hamburger-inner::after {
        -webkit-transform: translate3d(0, -20px, 0) rotate(-90deg);
        transform: translate3d(0, -20px, 0) rotate(-90deg);
    }

.hamburger--slider-r .hamburger-inner {
    top: 2px;
}

    .hamburger--slider-r .hamburger-inner::before {
        top: 10px;
        -webkit-transition-property: opacity, -webkit-transform;
        transition-property: opacity, -webkit-transform;
        transition-property: transform, opacity;
        transition-property: transform, opacity, -webkit-transform;
        -webkit-transition-timing-function: ease;
        transition-timing-function: ease;
        -webkit-transition-duration: 0.15s;
        transition-duration: 0.15s;
    }

    .hamburger--slider-r .hamburger-inner::after {
        top: 20px;
    }

.hamburger--slider-r.is-active .hamburger-inner {
    -webkit-transform: translate3d(0, 10px, 0) rotate(-45deg);
    transform: translate3d(0, 10px, 0) rotate(-45deg);
}

    .hamburger--slider-r.is-active .hamburger-inner::before {
        -webkit-transform: rotate(45deg) translate3d(5.71429px, -6px, 0);
        transform: rotate(45deg) translate3d(5.71429px, -6px, 0);
        opacity: 0;
    }

    .hamburger--slider-r.is-active .hamburger-inner::after {
        -webkit-transform: translate3d(0, -20px, 0) rotate(90deg);
        transform: translate3d(0, -20px, 0) rotate(90deg);
    }

.hamburger--spin .hamburger-inner {
    -webkit-transition-duration: 0.22s;
    transition-duration: 0.22s;
    -webkit-transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

    .hamburger--spin .hamburger-inner::before {
        -webkit-transition: top 0.1s 0.25s ease-in, opacity 0.1s ease-in;
        transition: top 0.1s 0.25s ease-in, opacity 0.1s ease-in;
    }

    .hamburger--spin .hamburger-inner::after {
        -webkit-transition: bottom 0.1s 0.25s ease-in, -webkit-transform 0.22s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.1s 0.25s ease-in, -webkit-transform 0.22s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.1s 0.25s ease-in, transform 0.22s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.1s 0.25s ease-in, transform 0.22s cubic-bezier(0.55, 0.055, 0.675, 0.19), -webkit-transform 0.22s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }

.hamburger--spin.is-active .hamburger-inner {
    -webkit-transform: rotate(225deg);
    transform: rotate(225deg);
    -webkit-transition-delay: 0.12s;
    transition-delay: 0.12s;
    -webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}

    .hamburger--spin.is-active .hamburger-inner::before {
        top: 0;
        opacity: 0;
        -webkit-transition: top 0.1s ease-out, opacity 0.1s 0.12s ease-out;
        transition: top 0.1s ease-out, opacity 0.1s 0.12s ease-out;
    }

    .hamburger--spin.is-active .hamburger-inner::after {
        bottom: 0;
        -webkit-transform: rotate(-90deg);
        transform: rotate(-90deg);
        -webkit-transition: bottom 0.1s ease-out, -webkit-transform 0.22s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.1s ease-out, -webkit-transform 0.22s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.1s ease-out, transform 0.22s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.1s ease-out, transform 0.22s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.22s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1);
    }

.hamburger--spin-r .hamburger-inner {
    -webkit-transition-duration: 0.22s;
    transition-duration: 0.22s;
    -webkit-transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

    .hamburger--spin-r .hamburger-inner::before {
        -webkit-transition: top 0.1s 0.25s ease-in, opacity 0.1s ease-in;
        transition: top 0.1s 0.25s ease-in, opacity 0.1s ease-in;
    }

    .hamburger--spin-r .hamburger-inner::after {
        -webkit-transition: bottom 0.1s 0.25s ease-in, -webkit-transform 0.22s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.1s 0.25s ease-in, -webkit-transform 0.22s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.1s 0.25s ease-in, transform 0.22s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.1s 0.25s ease-in, transform 0.22s cubic-bezier(0.55, 0.055, 0.675, 0.19), -webkit-transform 0.22s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }

.hamburger--spin-r.is-active .hamburger-inner {
    -webkit-transform: rotate(-225deg);
    transform: rotate(-225deg);
    -webkit-transition-delay: 0.12s;
    transition-delay: 0.12s;
    -webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}

    .hamburger--spin-r.is-active .hamburger-inner::before {
        top: 0;
        opacity: 0;
        -webkit-transition: top 0.1s ease-out, opacity 0.1s 0.12s ease-out;
        transition: top 0.1s ease-out, opacity 0.1s 0.12s ease-out;
    }

    .hamburger--spin-r.is-active .hamburger-inner::after {
        bottom: 0;
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
        -webkit-transition: bottom 0.1s ease-out, -webkit-transform 0.22s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.1s ease-out, -webkit-transform 0.22s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.1s ease-out, transform 0.22s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.1s ease-out, transform 0.22s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.22s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1);
    }

.hamburger--spring .hamburger-inner {
    top: 2px;
    -webkit-transition: background-color 0s 0.13s linear;
    transition: background-color 0s 0.13s linear;
}

    .hamburger--spring .hamburger-inner::before {
        top: 10px;
        -webkit-transition: top 0.1s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.1s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.1s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.1s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }

    .hamburger--spring .hamburger-inner::after {
        top: 20px;
        -webkit-transition: top 0.2s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.2s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.2s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.2s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }

.hamburger--spring.is-active .hamburger-inner {
    -webkit-transition-delay: 0.22s;
    transition-delay: 0.22s;
    background-color: transparent;
}

    .hamburger--spring.is-active .hamburger-inner::before {
        top: 0;
        -webkit-transition: top 0.1s 0.15s cubic-bezier(0.33333, 0, 0.66667, 0.33333), -webkit-transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.1s 0.15s cubic-bezier(0.33333, 0, 0.66667, 0.33333), -webkit-transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.1s 0.15s cubic-bezier(0.33333, 0, 0.66667, 0.33333), transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.1s 0.15s cubic-bezier(0.33333, 0, 0.66667, 0.33333), transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1);
        -webkit-transform: translate3d(0, 10px, 0) rotate(45deg);
        transform: translate3d(0, 10px, 0) rotate(45deg);
    }

    .hamburger--spring.is-active .hamburger-inner::after {
        top: 0;
        -webkit-transition: top 0.2s cubic-bezier(0.33333, 0, 0.66667, 0.33333), -webkit-transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.2s cubic-bezier(0.33333, 0, 0.66667, 0.33333), -webkit-transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.2s cubic-bezier(0.33333, 0, 0.66667, 0.33333), transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.2s cubic-bezier(0.33333, 0, 0.66667, 0.33333), transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1);
        -webkit-transform: translate3d(0, 10px, 0) rotate(-45deg);
        transform: translate3d(0, 10px, 0) rotate(-45deg);
    }

.hamburger--spring-r .hamburger-inner {
    top: auto;
    bottom: 0;
    -webkit-transition-duration: 0.13s;
    transition-duration: 0.13s;
    -webkit-transition-delay: 0s;
    transition-delay: 0s;
    -webkit-transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

    .hamburger--spring-r .hamburger-inner::after {
        top: -20px;
        -webkit-transition: top 0.2s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), opacity 0s linear;
        transition: top 0.2s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), opacity 0s linear;
    }

    .hamburger--spring-r .hamburger-inner::before {
        -webkit-transition: top 0.1s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.1s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.1s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.1s 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1), transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19), -webkit-transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }

.hamburger--spring-r.is-active .hamburger-inner {
    -webkit-transform: translate3d(0, -10px, 0) rotate(-45deg);
    transform: translate3d(0, -10px, 0) rotate(-45deg);
    -webkit-transition-delay: 0.22s;
    transition-delay: 0.22s;
    -webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}

    .hamburger--spring-r.is-active .hamburger-inner::after {
        top: 0;
        opacity: 0;
        -webkit-transition: top 0.2s cubic-bezier(0.33333, 0, 0.66667, 0.33333), opacity 0s 0.22s linear;
        transition: top 0.2s cubic-bezier(0.33333, 0, 0.66667, 0.33333), opacity 0s 0.22s linear;
    }

    .hamburger--spring-r.is-active .hamburger-inner::before {
        top: 0;
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
        -webkit-transition: top 0.1s 0.15s cubic-bezier(0.33333, 0, 0.66667, 0.33333), -webkit-transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.1s 0.15s cubic-bezier(0.33333, 0, 0.66667, 0.33333), -webkit-transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.1s 0.15s cubic-bezier(0.33333, 0, 0.66667, 0.33333), transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.1s 0.15s cubic-bezier(0.33333, 0, 0.66667, 0.33333), transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.13s 0.22s cubic-bezier(0.215, 0.61, 0.355, 1);
    }

.hamburger--stand .hamburger-inner {
    -webkit-transition: background-color 0s 0.075s linear, -webkit-transform 0.075s 0.15s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    transition: background-color 0s 0.075s linear, -webkit-transform 0.075s 0.15s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    transition: transform 0.075s 0.15s cubic-bezier(0.55, 0.055, 0.675, 0.19), background-color 0s 0.075s linear;
    transition: transform 0.075s 0.15s cubic-bezier(0.55, 0.055, 0.675, 0.19), background-color 0s 0.075s linear, -webkit-transform 0.075s 0.15s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

    .hamburger--stand .hamburger-inner::before {
        -webkit-transition: top 0.075s 0.075s ease-in, -webkit-transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.075s 0.075s ease-in, -webkit-transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.075s 0.075s ease-in, transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.075s 0.075s ease-in, transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19), -webkit-transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }

    .hamburger--stand .hamburger-inner::after {
        -webkit-transition: bottom 0.075s 0.075s ease-in, -webkit-transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.075s 0.075s ease-in, -webkit-transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.075s 0.075s ease-in, transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.075s 0.075s ease-in, transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19), -webkit-transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }

.hamburger--stand.is-active .hamburger-inner {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    background-color: transparent;
    -webkit-transition: background-color 0s 0.15s linear, -webkit-transform 0.075s 0s cubic-bezier(0.215, 0.61, 0.355, 1);
    transition: background-color 0s 0.15s linear, -webkit-transform 0.075s 0s cubic-bezier(0.215, 0.61, 0.355, 1);
    transition: transform 0.075s 0s cubic-bezier(0.215, 0.61, 0.355, 1), background-color 0s 0.15s linear;
    transition: transform 0.075s 0s cubic-bezier(0.215, 0.61, 0.355, 1), background-color 0s 0.15s linear, -webkit-transform 0.075s 0s cubic-bezier(0.215, 0.61, 0.355, 1);
}

    .hamburger--stand.is-active .hamburger-inner::before {
        top: 0;
        -webkit-transform: rotate(-45deg);
        transform: rotate(-45deg);
        -webkit-transition: top 0.075s 0.1s ease-out, -webkit-transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.075s 0.1s ease-out, -webkit-transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.075s 0.1s ease-out, transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.075s 0.1s ease-out, transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    .hamburger--stand.is-active .hamburger-inner::after {
        bottom: 0;
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg);
        -webkit-transition: bottom 0.075s 0.1s ease-out, -webkit-transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.075s 0.1s ease-out, -webkit-transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.075s 0.1s ease-out, transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.075s 0.1s ease-out, transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
    }

.hamburger--stand-r .hamburger-inner {
    -webkit-transition: background-color 0s 0.075s linear, -webkit-transform 0.075s 0.15s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    transition: background-color 0s 0.075s linear, -webkit-transform 0.075s 0.15s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    transition: transform 0.075s 0.15s cubic-bezier(0.55, 0.055, 0.675, 0.19), background-color 0s 0.075s linear;
    transition: transform 0.075s 0.15s cubic-bezier(0.55, 0.055, 0.675, 0.19), background-color 0s 0.075s linear, -webkit-transform 0.075s 0.15s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

    .hamburger--stand-r .hamburger-inner::before {
        -webkit-transition: top 0.075s 0.075s ease-in, -webkit-transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.075s 0.075s ease-in, -webkit-transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.075s 0.075s ease-in, transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: top 0.075s 0.075s ease-in, transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19), -webkit-transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }

    .hamburger--stand-r .hamburger-inner::after {
        -webkit-transition: bottom 0.075s 0.075s ease-in, -webkit-transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.075s 0.075s ease-in, -webkit-transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.075s 0.075s ease-in, transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.075s 0.075s ease-in, transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19), -webkit-transform 0.075s 0s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }

.hamburger--stand-r.is-active .hamburger-inner {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
    background-color: transparent;
    -webkit-transition: background-color 0s 0.15s linear, -webkit-transform 0.075s 0s cubic-bezier(0.215, 0.61, 0.355, 1);
    transition: background-color 0s 0.15s linear, -webkit-transform 0.075s 0s cubic-bezier(0.215, 0.61, 0.355, 1);
    transition: transform 0.075s 0s cubic-bezier(0.215, 0.61, 0.355, 1), background-color 0s 0.15s linear;
    transition: transform 0.075s 0s cubic-bezier(0.215, 0.61, 0.355, 1), background-color 0s 0.15s linear, -webkit-transform 0.075s 0s cubic-bezier(0.215, 0.61, 0.355, 1);
}

    .hamburger--stand-r.is-active .hamburger-inner::before {
        top: 0;
        -webkit-transform: rotate(-45deg);
        transform: rotate(-45deg);
        -webkit-transition: top 0.075s 0.1s ease-out, -webkit-transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.075s 0.1s ease-out, -webkit-transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.075s 0.1s ease-out, transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: top 0.075s 0.1s ease-out, transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    .hamburger--stand-r.is-active .hamburger-inner::after {
        bottom: 0;
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg);
        -webkit-transition: bottom 0.075s 0.1s ease-out, -webkit-transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.075s 0.1s ease-out, -webkit-transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.075s 0.1s ease-out, transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.075s 0.1s ease-out, transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.075s 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
    }

.hamburger--squeeze .hamburger-inner {
    -webkit-transition-duration: 0.075s;
    transition-duration: 0.075s;
    -webkit-transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

    .hamburger--squeeze .hamburger-inner::before {
        -webkit-transition: top 0.075s 0.12s ease, opacity 0.075s ease;
        transition: top 0.075s 0.12s ease, opacity 0.075s ease;
    }

    .hamburger--squeeze .hamburger-inner::after {
        -webkit-transition: bottom 0.075s 0.12s ease, -webkit-transform 0.075s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.075s 0.12s ease, -webkit-transform 0.075s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.075s 0.12s ease, transform 0.075s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        transition: bottom 0.075s 0.12s ease, transform 0.075s cubic-bezier(0.55, 0.055, 0.675, 0.19), -webkit-transform 0.075s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }

.hamburger--squeeze.is-active .hamburger-inner {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-transition-delay: 0.12s;
    transition-delay: 0.12s;
    -webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}

    .hamburger--squeeze.is-active .hamburger-inner::before {
        top: 0;
        opacity: 0;
        -webkit-transition: top 0.075s ease, opacity 0.075s 0.12s ease;
        transition: top 0.075s ease, opacity 0.075s 0.12s ease;
    }

    .hamburger--squeeze.is-active .hamburger-inner::after {
        bottom: 0;
        -webkit-transform: rotate(-90deg);
        transform: rotate(-90deg);
        -webkit-transition: bottom 0.075s ease, -webkit-transform 0.075s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.075s ease, -webkit-transform 0.075s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.075s ease, transform 0.075s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1);
        transition: bottom 0.075s ease, transform 0.075s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.075s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1);
    }

.hamburger--vortex .hamburger-inner {
    -webkit-transition-duration: 0.2s;
    transition-duration: 0.2s;
    -webkit-transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
    transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
}

    .hamburger--vortex .hamburger-inner::before, .hamburger--vortex .hamburger-inner::after {
        -webkit-transition-duration: 0s;
        transition-duration: 0s;
        -webkit-transition-delay: 0.1s;
        transition-delay: 0.1s;
        -webkit-transition-timing-function: linear;
        transition-timing-function: linear;
    }

    .hamburger--vortex .hamburger-inner::before {
        -webkit-transition-property: top, opacity;
        transition-property: top, opacity;
    }

    .hamburger--vortex .hamburger-inner::after {
        -webkit-transition-property: bottom, -webkit-transform;
        transition-property: bottom, -webkit-transform;
        transition-property: bottom, transform;
        transition-property: bottom, transform, -webkit-transform;
    }

.hamburger--vortex.is-active .hamburger-inner {
    -webkit-transform: rotate(765deg);
    transform: rotate(765deg);
    -webkit-transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
    transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
}

    .hamburger--vortex.is-active .hamburger-inner::before, .hamburger--vortex.is-active .hamburger-inner::after {
        -webkit-transition-delay: 0s;
        transition-delay: 0s;
    }

    .hamburger--vortex.is-active .hamburger-inner::before {
        top: 0;
        opacity: 0;
    }

    .hamburger--vortex.is-active .hamburger-inner::after {
        bottom: 0;
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
    }

.hamburger--vortex-r .hamburger-inner {
    -webkit-transition-duration: 0.2s;
    transition-duration: 0.2s;
    -webkit-transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
    transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
}

    .hamburger--vortex-r .hamburger-inner::before, .hamburger--vortex-r .hamburger-inner::after {
        -webkit-transition-duration: 0s;
        transition-duration: 0s;
        -webkit-transition-delay: 0.1s;
        transition-delay: 0.1s;
        -webkit-transition-timing-function: linear;
        transition-timing-function: linear;
    }

    .hamburger--vortex-r .hamburger-inner::before {
        -webkit-transition-property: top, opacity;
        transition-property: top, opacity;
    }

    .hamburger--vortex-r .hamburger-inner::after {
        -webkit-transition-property: bottom, -webkit-transform;
        transition-property: bottom, -webkit-transform;
        transition-property: bottom, transform;
        transition-property: bottom, transform, -webkit-transform;
    }

.hamburger--vortex-r.is-active .hamburger-inner {
    -webkit-transform: rotate(-765deg);
    transform: rotate(-765deg);
    -webkit-transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
    transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
}

    .hamburger--vortex-r.is-active .hamburger-inner::before, .hamburger--vortex-r.is-active .hamburger-inner::after {
        -webkit-transition-delay: 0s;
        transition-delay: 0s;
    }

    .hamburger--vortex-r.is-active .hamburger-inner::before {
        top: 0;
        opacity: 0;
    }

    .hamburger--vortex-r.is-active .hamburger-inner::after {
        bottom: 0;
        -webkit-transform: rotate(-90deg);
        transform: rotate(-90deg);
    }

@font-face {
    font-family: "flaticon";
    src: url("flaticonef5e.ttf?4a85dd2954bc9fe13885879bb3e70d57") format("truetype"), url("flaticonef5e.woff?4a85dd2954bc9fe13885879bb3e70d57") format("woff"), url("flaticonef5e.woff2?4a85dd2954bc9fe13885879bb3e70d57") format("woff2"), url("flaticonef5e.eot?4a85dd2954bc9fe13885879bb3e70d57#iefix") format("embedded-opentype"), url("flaticonef5e.svg?4a85dd2954bc9fe13885879bb3e70d57#flaticon") format("svg");
}

i[class^="flaticon-"]:before, i[class*=" flaticon-"]:before {
    font-family: flaticon !important;
    font-style: normal;
    font-weight: normal !important;
    -webkit-font-feature-settings: normal;
    font-feature-settings: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.flaticon-protection:before {
    content: "";
}

.flaticon-stopwatch:before {
    content: "";
}

.flaticon-quality:before {
    content: "";
}

.flaticon-quality-1:before {
    content: "";
}

.flaticon-pdf:before {
    content: "";
}

.flaticon-instagram:before {
    content: "";
}

.flaticon-back-arrow:before {
    content: "";
}

.flaticon-expand:before {
    content: "";
}

.flaticon-pin:before {
    content: "";
}

.flaticon-telephone-call:before {
    content: "";
}

.flaticon-email:before {
    content: "";
}

.flaticon-pin-1:before {
    content: "";
}

.flaticon-whatsapp:before {
    content: "";
}

.flaticon-down-arrow:before {
    content: "";
}

.flaticon-ongoing:before {
    content: "";
}

.flaticon-building:before {
    content: "";
}

.flaticon-planned:before {
    content: "";
}

.flaticon-facebook-logo:before {
    content: "";
}

.flaticon-twitter:before {
    content: "";
}

.flaticon-behance:before {
    content: "";
}

.flaticon-pinterest:before {
    content: "";
}

.flaticon-next:before {
    content: "";
}

.flaticon-play:before {
    content: "";
}

.flaticon-idea-1:before {
    content: "";
}

.flaticon-hut:before {
    content: "";
}

.flaticon-next-1:before {
    content: "";
}

.flaticon-degrees:before {
    content: "";
}

*, *:before, *:after {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0;
    list-style: none;
    background-position: center;
    background-repeat: no-repeat;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    word-wrap: break-word;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    font-style: normal;
    outline: 0;
    background-color: transparent;
    resize: none;
    font-family: 'Cairo', sans-serif;
}

[lang="ar"] *, [lang="ar"] *:before, [lang="ar"] *:after {
    font-family: 'Cairo', sans-serif;
}

.fa:before, .far:before, .fas:before, .fab:before {
    font-family: "Font Awesome 5 Free";
}

html, body {
    overflow-x: hidden;
}

a {
    color: inherit;
    font-weight: inherit;
    text-decoration: none;
}

select, form button, button, .slick-dots li {
    cursor: pointer;
}

.clear:after {
    content: "";
    display: block;
    clear: both;
}

.grid {
    max-width: 1700px;
    position: relative;
    margin-left: auto;
    margin-right: auto;
}

@media (max-width: 1720px) {
    .grid {
        margin-left: 10px;
        margin-right: 10px;
    }
}

.one > div {
    margin: 0 -1%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

    .one > div > * {
        width: 98%;
        margin: 0 1% 20px;
    }

.tow > div {
    margin: 0 -1%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

    .tow > div > * {
        width: 48%;
        margin: 0 1% 20px;
    }

h5 {
    font-weight: 600;
    font-size: 36px;
    color: #142333;
    text-transform: capitalize;
    position: relative;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

    h5:before {
        content: "";
        position: absolute;
        width: 130px;
        height: 3px;
        background-color: #bf9d7f;
        left: 0;
        bottom: 0;
    }

    h5 + p {
        font-weight: 400;
        font-size: 17px;
    }
.projects h2 {
    font-weight: 600;
    font-size: 36px;
    color: #142333;
    text-transform: capitalize;
    position: relative;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.projects h2:before {
    content: "";
    position: absolute;
    width: 130px;
    height: 3px;
    background-color: #bf9d7f;
    bottom: 0;
}

    .projects h2 + p {
        font-weight: 400;
        font-size: 17px;
    }

@-webkit-keyframes sliderbuyutme {
    from {
        -webkit-transform: scale(1);
        transform: scale(1);
    }

    to {
        -webkit-transform: scale(1.12);
        transform: scale(1.12);
    }
}

@keyframes sliderbuyutme {
    from {
        -webkit-transform: scale(1);
        transform: scale(1);
    }

    to {
        -webkit-transform: scale(1.12);
        transform: scale(1.12);
    }
}

.slogantext {
    padding: 25px 0 0;
    position: relative;
    font-weight: 600;
    font-size: 30px;
    color: #142333;
}

    .slogantext span {
        color: #bf9d7f;
    }
  

    .slogantext:before {
        content: "";
        font-weight: 600;
        font-size: 200px;
        position: absolute;
        left: -100px;
        top: 45px;
        -webkit-text-stroke: 1px #bf9d7f;
        color: transparent;
    }

form {
    text-align: left;
    padding: 30px 0;
}

    form label {
        font-weight: 700;
        font-size: 17px;
        color: #142333;
        text-transform: capitalize;
        display: block;
        margin-bottom: 10px;
    }

    form input, form select, form textarea, form .upload {
        border: 1px solid #e2e2e2;
        border-radius: 3px;
        background-color: #fff;
        width: 100%;
        height: 60px;
        padding: 0 20px;
        font-weight: 400;
        font-size: 15px;
        color: #142333;
        -webkit-transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
    }

        form input[type="file"] {
            display: none
        }

        form .upload div {
            padding: 10px 20px;
            margin: 10px 0;
            text-transform: capitalize;
            background-color: #bf9d7f;
            border-radius: 3px;
            display: inline-block;
            color: #fff
        }

        form input:hover, form select:hover, form textarea:hover {
            -webkit-transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
        }

        form input:focus, form select:focus, form textarea:focus {
            border-color: #142333;
        }

    form select {
        text-transform: capitalize;
    }

    form textarea {
        padding: 15px;
        height: 150px;
    }

    form .button {
        text-align: center;
    }

        form .button button {
            padding: 0 30px;
            height: 50px;
            border-radius: 3px;
            background-color: #bf9d7f;
            font-weight: 500;
            font-size: 15px;
            color: #fff;
            text-transform: uppercase;
            -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        }

            form .button button:hover {
                -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
            }

            form .button button:hover {
                -webkit-transform: translateY(-5px);
                transform: translateY(-5px);
            }

.loading {
    position: fixed;
    z-index: 999;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #142333;
}

    .loading svg {
        width: 450px;
        position: absolute;
        left: 50%;
        top: 50%;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
    }

        .loading svg path, .loading svg polygon {
            -webkit-animation-name: loading;
            animation-name: loading;
            -webkit-animation-duration: 15s;
            animation-duration: 15s;
            stroke-dasharray: 3319;
            stroke-dashoffset: 3319;
            -webkit-animation-fill-mode: forwards;
            animation-fill-mode: forwards;
            fill-opacity: 0;
            stroke: #fff;
            stroke-width: .1px;
        }

@-webkit-keyframes loading {
    7% {
        fill-opacity: 0;
        stroke-width: .2px;
    }

    10% {
        fill-opacity: 1;
        stroke-width: 0;
    }

    60% {
        stroke-dashoffset: 0;
    }

    to {
        stroke-width: 0;
        fill-opacity: 1;
        stroke-dashoffset: 0;
    }
}

@keyframes loading {
    7% {
        fill-opacity: 0;
        stroke-width: .2px;
    }

    10% {
        fill-opacity: 1;
        stroke-width: 0;
    }

    60% {
        stroke-dashoffset: 0;
    }

    to {
        stroke-width: 0;
        fill-opacity: 1;
        stroke-dashoffset: 0;
    }
}

header {
    position: fixed;
    width: 100%;
    left: 0;
    padding: 17px 0 100px;
    z-index: 99;
    background: black;
    background: -webkit-gradient(linear, left top, left bottom, from(black), to(transparent));
    background: linear-gradient(black, transparent);
    -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
    transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
}

    header:hover {
        -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
    }

    header > div {
        max-width: 1170px;
        position: relative;
        margin-left: auto;
        margin-right: auto;
    }

@media (max-width: 1190px) {
    header > div {
        margin-left: 10px;
        margin-right: 10px;
    }
}

header > div:after {
    content: "";
    display: block;
    clear: both;
}

header > div.top {
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(191, 157, 127, .5);
    margin-bottom: 10px;
    font-size: 15px;
    -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
    transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
}

    header > div.top:after {
        content: "";
        display: block;
        clear: both;
    }

    header > div.top > div {
        margin: 0 0;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        header > div.top > div > * {
            margin: 0 0 0;
        }

        header > div.top > div > .contact {
            width: 40%
        }

        header > div.top > div > .social {
            width: 60%
        }

    header > div.top:hover {
        -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
    }

    header > div.top a {
        color: #fff;
        margin: 0 10px 0 0;
    }

        header > div.top a i, header > div.top a svg {
            color: #bf9d7f;
            fill: #bf9d7f;
            display: inline-block;
            -webkit-transform: translateY(3px);
            transform: translateY(3px);
            -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        }

        header > div.top a svg {
            margin-right: 5px;
            transform: translateY(0);
        }

        header > div.top a i:hover {
            -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        }

        header > div.top a:hover {
            color: #bf9d7f;
        }

            header > div.top a:hover i {
                -webkit-transform: translateY(0);
                transform: translateY(0);
            }

    header > div.top .social {
        text-align: right;
    }

        header > div.top .social a {
            margin: 0 0 0 10px;
        }

header > div.bottom > a {
    float: left;
}

    header > div.bottom > a img {
        width: 200px;
    }

header > div.bottom button {
    float: right;
    padding: 7px 0;
    display: none;
}

header > div.bottom > ul {
    float: right;
}

    header > div.bottom > ul li {
        display: inline-block;
        margin: 0 9px;
    }

        header > div.bottom > ul li a {
            display: block;
            font-weight: 500;
            font-size: 17px;
            color: #fff;
            text-transform: capitalize;
            padding: 8px 0;
            border-bottom: 2px solid transparent;
            -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        }

            header > div.bottom > ul li a:hover {
                -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            }

        header > div.bottom > ul li ul {
            position: absolute;
            padding: 10px 0;
            min-width: 160px;
            display: none;
        }

            header > div.bottom > ul li ul li {
                background-color: #142333;
                display: block;
                margin: 0;
            }

                header > div.bottom > ul li ul li a {
                    padding: 12px 50px 12px 30px;
                }

                    header > div.bottom > ul li ul li a:hover {
                        background-color: rgba(255, 255, 255, .1);
                    }

        header > div.bottom > ul li.offer a {
            background-color: #142333;
            border: 1px solid #bf9d7f;
            border-radius: 3px;
            font-size: 15px;
            padding: 8px 30px;
        }

            header > div.bottom > ul li.offer a:hover {
                border: 1px solid #142333;
                background-color: #bf9d7f;
            }

        header > div.bottom > ul li.lang a {
            cursor: pointer;
            padding: 2px 0 7px;
        }

        header > div.bottom > ul li.lang img {
            -webkit-transform: translateY(5px);
            transform: translateY(5px);
        }

        header > div.bottom > ul li.lang i {
            display: inline-block;
            -webkit-transform: translateY(4px);
            transform: translateY(4px);
            -webkit-transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
        }

            header > div.bottom > ul li.lang i:hover {
                -webkit-transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
            }

            header > div.bottom > ul li.lang i.ters {
                -webkit-transform: translateY(-4px) rotate(180deg);
                transform: translateY(-4px) rotate(180deg);
            }

        header > div.bottom > ul li.lang ul li a {
            padding: 8px 12px;
        }

        header > div.bottom > ul li:hover > a {
            border-bottom: 2px solid #bf9d7f;
        }

header.small {
    padding: 20px 0 10px;
    background: transparent;
    background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(transparent));
    background: linear-gradient(transparent, transparent);
    background-color: #142333;
}

    header.small > div.top {
        height: 0;
        overflow: hidden;
        padding: 0;
        border: 0;
        margin-bottom: 0;
    }

    header.small > div.bottom > ul li ul {
        padding: 21px 0;
    }

header.color {
    background-color: #142333;
    padding: 15px 0;
}

section {
    position: relative;
}

    section.slider a {
        height: 90vh;
        position: relative;
        background-color: #142333;
    }

        section.slider a img {
            position: absolute;
            width: 0;
            height: 90vh;
            -o-object-fit: cover;
            object-fit: cover;
            opacity: .03;
            -webkit-transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
            -webkit-transition-delay: .2s;
            transition-delay: .2s;
        }

            section.slider a img:hover {
                -webkit-transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
            }

            section.slider a img:nth-of-type(1) {
                left: 0;
                -webkit-transform-origin: 100% 50%;
                transform-origin: 100% 50%;
                -webkit-animation-duration: 6s;
                animation-duration: 6s;
            }

            section.slider a img:nth-of-type(2) {
                left: 50%;
                -webkit-transform-origin: 0 50%;
                transform-origin: 0 50%;
                -webkit-animation-duration: 14s;
                animation-duration: 14s;
            }

        section.slider a div {
            max-width: 1170px;
            position: relative;
            margin-left: auto;
            margin-right: auto;
            z-index: 1;
            padding: 30vh 100px 0 0;
            color: #fff;
        }

@media (max-width: 1190px) {
    section.slider a div {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.slider a div h2 {
    font-weight: 600;
    font-size: 65px;
    margin-bottom: 50px;
}

section.slider a div button {
    font-weight: 400;
    font-size: 22px;
    color: #fff;
    text-transform: uppercase;
    position: relative;
    padding: 0 0 10px;
    -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
    transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
}

    section.slider a div button:hover {
        -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
    }

    section.slider a div button:before {
        content: "";
        position: absolute;
        left: 0;
        bottom: 0;
        width: 55%;
        height: 3px;
        background-color: #fff;
        -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
    }

        section.slider a div button:before:hover {
            -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
        }

    section.slider a div button:hover {
        letter-spacing: 5px;
    }

        section.slider a div button:hover:before {
            width: 105%;
        }

section.slider a.slick-active img {
    opacity: .5;
    width: 50%;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-name: sliderbuyutme;
    animation-name: sliderbuyutme;
}

section.slider .slick-dots {
    max-width: 1170px;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    margin: -100px auto 50px;
}

@media (max-width: 1190px) {
    section.slider .slick-dots {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.slider .slick-dots li {
    display: inline-block;
    cursor: pointer;
}

    section.slider .slick-dots li button {
        padding: 10px;
        margin: 0 10px;
        font-weight: 500;
        font-size: 20px;
        color: #fff;
        border-bottom: 3px solid transparent;
        -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
    }

        section.slider .slick-dots li button:hover {
            -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        }

        section.slider .slick-dots li button:before {
            content: "0";
        }

    section.slider .slick-dots li.slick-active button {
        border-color: #fff;
    }

    section.slider .slick-dots li:hover button {
        border-color: rgba(255, 255, 255, .5);
    }

section.projects {
    padding-bottom: 70px;
}

    section.projects > div {
        max-width: 1170px;
        position: relative;
        margin-left: auto;
        margin-right: auto;
    }

@media (max-width: 1190px) {
    section.projects > div {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.projects > div .slogan {
    margin-bottom: 50px;
    position: relative;
    z-index: 1;
}

    section.projects > div .slogan:after {
        content: "";
        display: block;
        clear: both;
    }

    section.projects > div .slogan > p {
        float: left;
        width: 50%;
    }

    section.projects > div .slogan a {
        float: right;
        padding: 35px 40px;
        border: 1px solid #bf9d7f;
        margin-top: -250px;
        text-align: center;
        border-radius: 3px;
        -webkit-transition: all 0.7s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.7s cubic-bezier(0.65, 0, 0.35, 1);
    }

        section.projects > div .slogan a:hover {
            -webkit-transition: all 0.7s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.7s cubic-bezier(0.65, 0, 0.35, 1);
        }

        section.projects > div .slogan a img:nth-of-type(1) {
            width: 215px;
        }

        section.projects > div .slogan a img:nth-of-type(2) {
            max-width: 275px;
            display: block;
            border: 1px solid #bf9d7f;
            margin: 0 auto;
        }

        section.projects > div .slogan a p {
            font-weight: 400;
            font-size: 21px;
            color: #fff;
            margin: 15px 0 25px;
        }

            section.projects > div .slogan a p span {
                display: block;
                font-weight: 600;
            }

        section.projects > div .slogan a button {
            margin-top: 35px;
            padding: 17px 50px;
            background-color: #142333;
            font-weight: 500;
            font-size: 15px;
            color: #bf9d7f;
            text-transform: uppercase;
            border-radius: 3px;
            border: 1px solid #bf9d7f;
            -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
        }

            section.projects > div .slogan a button:hover {
                -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
            }

        section.projects > div .slogan a:hover {
            background-color: #142333;
        }

            section.projects > div .slogan a:hover button:hover {
                background-color: #bf9d7f;
                color: #142333;
            }

section.projects > div .projects {
    position: relative;
    z-index: 1;
}

    section.projects > div .projects > div {
        margin: 0 -2%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.projects > div .projects > div > * {
            width: 46%;
            margin: 0 2% 0;
        }

    section.projects > div .projects img {
        width: 100%;
    }

    section.projects > div .projects p {
        font-size: 19px;
        color: #20363c;
        line-height: 1.4;
        margin-bottom: 40px;
        text-align: justify;
    }

    section.projects > div .projects ul {
        display: inline-block;
        width: 48%;
        position: relative;
    }

        section.projects > div .projects ul:before {
            content: "";
            position: absolute;
            width: 65px;
            height: 1px;
            background-color: #e8e8e8;
            left: 0;
            top: 0;
            -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
        }

            section.projects > div .projects ul:before:hover {
                -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
            }

        section.projects > div .projects ul li {
            padding: 15px 0 15px 40px;
            font-weight: 500;
            font-size: 18px;
            color: #142333;
            text-transform: capitalize;
            position: relative;
            -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        }

            section.projects > div .projects ul li:hover {
                -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
            }

            section.projects > div .projects ul li i {
                line-height: 0;
                color: #bf9d7f;
                -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                top: 50%;
                -webkit-transform: translateY(-50%);
                transform: translateY(-50%);
            }

                section.projects > div .projects ul li i:hover {
                    -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                    transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                }

                section.projects > div .projects ul li i:nth-of-type(1) {
                    position: absolute;
                    font-size: 30px;
                    left: 0;
                }

                section.projects > div .projects ul li i:nth-of-type(2) {
                    right: -45px;
                    display: inline-block;
                    -webkit-transform: translateY(2px);
                    transform: translateY(2px);
                    margin-left: 5px;
                }

            section.projects > div .projects ul li:before {
                content: "";
                position: absolute;
                width: 65px;
                height: 1px;
                background-color: #e8e8e8;
                left: 0;
                bottom: 0;
                -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
            }

                section.projects > div .projects ul li:before:hover {
                    -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
                    transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
                }

            section.projects > div .projects ul li:hover {
                letter-spacing: 1.5px;
            }

                section.projects > div .projects ul li:hover i:nth-of-type(2) {
                    opacity: 1;
                }

                section.projects > div .projects ul li:hover:before {
                    width: 50%;
                }

    section.projects > div .projects .button {
        margin-top: 35px;
        padding: 17px 50px;
        background-color: #bf9d7f;
        font-weight: 500;
        font-size: 15px;
        color: #fff;
        text-transform: uppercase;
        border-radius: 3px;
        display: inline-block;
        -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
    }

        section.projects > div .projects .button:hover {
            -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        }

        section.projects > div .projects .button:hover {
            background-color: #142333;
            color: #bf9d7f;
        }

section.projects > div .background {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

section.gallery {
    background-color: #ecebe9;
    border-top: 1px solid #c8c8c8;
    border-bottom: 1px solid #c8c8c8;
    padding: 65px 0 40px;
    position: relative;
    text-align: center;
    overflow: hidden;
}

    section.gallery > p {
        position: absolute;
        width: 5000px;
        left: calc(50% - 2500px);
        text-align: center;
        font-weight: 600;
        font-size: 810px;
        color: #f0f0f0;
        text-transform: uppercase;
        -webkit-text-stroke: 1px #ddd;
    }

    section.gallery .background {
        position: absolute;
        width: 100%;
        top: 300px;
    }

    section.gallery .grid {
        max-width: 1170px;
        position: relative;
        margin-left: auto;
        margin-right: auto;
    }

@media (max-width: 1190px) {
    section.gallery .grid {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.gallery .grid h5:before {
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
}

section.gallery .grid .gallery {
    background-color: #142333;
    /*background-image: url(../img/tv.webp);*/
    background-size: contain;
    height: 685px;
    position: relative;
    text-align: left;
}

    section.gallery .grid .gallery a {
        position: absolute;
        overflow: hidden;
    }

        section.gallery .grid .gallery a img {
            width: 100%;
            height: 100%;
            -o-object-fit: cover;
            object-fit: cover;
            opacity: .3;
            -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        }

            section.gallery .grid .gallery a img:hover {
                -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            }

        section.gallery .grid .gallery a h3 {
            position: absolute;
            z-index: 1;
            left: 40px;
            right: 20px;
            bottom: -100px;
            font-weight: 600;
            font-size: 19px;
            color: #fff;
            text-shadow: 0 2px 3px rgba(36, 48, 59, .4);
            padding-bottom: 20px;
            -webkit-transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
        }

            section.gallery .grid .gallery a h3:hover {
                -webkit-transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
            }

            section.gallery .grid .gallery a h3:before {
                content: "";
                position: absolute;
                width: 130px;
                height: 3px;
                background-color: #bf9d7f;
                left: 0;
                bottom: 0;
            }
        section.gallery .grid .gallery a h2 {
            position: absolute;
            z-index: 1;
            left: 40px;
            right: 20px;
            bottom: -100px;
            font-weight: 600;
            font-size: 19px;
            color: #fff;
            text-shadow: 0 2px 3px rgba(36, 48, 59, .4);
            padding-bottom: 20px;
            -webkit-transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
        }

            section.gallery .grid .gallery a h2:hover {
                -webkit-transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
            }

            section.gallery .grid .gallery a h2:before {
                content: "";
                position: absolute;
                width: 130px;
                height: 3px;
                background-color: #bf9d7f;
                left: 0;
                bottom: 0;
            }

        section.gallery .grid .gallery a div {
            position: absolute;
            left: 50%;
            top: 50%;
            -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
            z-index: 2;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 1px solid #fff;
        }

            section.gallery .grid .gallery a div div {
                width: 100px;
                height: 100px;
                background-color: rgba(20, 35, 51, .42);
            }

                section.gallery .grid .gallery a div div i {
                    font-size: 40px;
                    color: #bf9d7f;
                    position: absolute;
                    line-height: 1;
                    left: 50%;
                    top: 50%;
                    -webkit-transform: translate(-50%, -50%);
                    transform: translate(-50%, -50%);
                }

        section.gallery .grid .gallery a:hover img {
            opacity: .75;
            -webkit-transform: scale(1.1);
            transform: scale(1.1);
        }

        section.gallery .grid .gallery a:hover h3 {
            bottom: 25px;
        }
        section.gallery .grid .gallery a:hover h2 {
            bottom: 25px;
        }
        section.gallery .grid .gallery a:nth-child(1) {
            left: 25%;
            top: 0;
            width: 50%;
            height: 66.66%;
        }

        section.gallery .grid .gallery a:nth-child(2) {
            left: 0;
            top: 0;
            width: 25%;
            height: 33.33%;
        }

        section.gallery .grid .gallery a:nth-child(3) {
            left: 0;
            top: 33.33%;
            width: 25%;
            height: 66.66%;
        }

        section.gallery .grid .gallery a:nth-child(4) {
            left: 25%;
            top: 66.66%;
            width: 25%;
            height: 33.33%;
        }

        section.gallery .grid .gallery a:nth-child(5) {
            right: 0;
            top: 0;
            width: 25%;
            height: 33.3%;
        }

        section.gallery .grid .gallery a:nth-child(6) {
            right: 0;
            top: 33.33%;
            width: 25%;
            height: 33.33%;
        }

        section.gallery .grid .gallery a:nth-child(7) {
            right: 0;
            top: 66.66%;
            width: 50%;
            height: 33.33%;
        }

section.gallery .button {
    margin-top: 35px;
    padding: 17px 50px;
    background-color: #bf9d7f;
    font-weight: 500;
    font-size: 15px;
    color: #fff;
    text-transform: uppercase;
    border-radius: 3px;
    display: inline-block;
    -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
    transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
}

    section.gallery .button:hover {
        -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
    }

    section.gallery .button:hover {
        background-color: #142333;
        color: #bf9d7f;
    }

section.home-down {
    padding: 60px 0 35px;
}
    section.home-down > div .projects ul {
        display: inline-block;
        width: 48%;
        position: relative;
    }

        section.home-down > div .projects ul:before {
            content: "";
            position: absolute;
            width: 65px;
            height: 1px;
            background-color: #e8e8e8;
            left: 0;
            top: 0;
            -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
        }

            section.home-down > div .projects ul:before:hover {
                -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
            }

        section.home-down > div .projects ul li {
            padding: 15px 0 15px 40px;
            font-weight: 500;
            font-size: 18px;
            color: #142333;
            text-transform: capitalize;
            position: relative;
            -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        }

            section.home-down > div .projects ul li:hover {
                -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
            }

            section.home-down > div .projects ul li i {
                line-height: 0;
                color: #bf9d7f;
                -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                top: 50%;
                -webkit-transform: translateY(-50%);
                transform: translateY(-50%);
            }

                section.home-down > div .projects ul li i:hover {
                    -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                    transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                }

                section.home-down > div .projects ul li i:nth-of-type(1) {
                    position: absolute;
                    font-size: 30px;
                    left: 0;
                }

                section.home-down > div .projects ul li i:nth-of-type(2) {
                    right: -45px;
                    display: inline-block;
                    -webkit-transform: translateY(2px);
                    transform: translateY(2px);
                    margin-left: 5px;
                }

            section.home-down > div .projects ul li:before {
                content: "";
                position: absolute;
                width: 65px;
                height: 1px;
                background-color: #e8e8e8;
                left: 0;
                bottom: 0;
                -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
            }

                section.home-down > div .projects ul li:before:hover {
                    -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
                    transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
                }

            section.home-down > div .projects ul li:hover {
                letter-spacing: 1.5px;
            }

                section.home-down > div .projects ul li:hover i:nth-of-type(2) {
                    opacity: 1;
                }

                section.home-down > div .projects ul li:hover:before {
                    width: 50%;
                }
    section.home-down .nav > div {
        max-width: 1170px;
        position: relative;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 85px;
    }

        section.home-down .nav > div > div {
            margin: 0 -1%;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-wrap: wrap;
            flex-wrap: wrap;
        }

            section.home-down .nav > div > div > * {
                width: 31.33333%;
                margin: 0 1% 0;
            }

@media (max-width: 1190px) {
    section.home-down .nav > div {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.home-down .nav > div .item {
    position: relative;
    padding: 40px 25px;
    border-radius: 3px;
    overflow: hidden;
    height: 535px;
}

    section.home-down .nav > div .item h5 {
        font-size: 29px;
        color: #fff;
        position: relative;
        z-index: 2;
    }

    section.home-down .nav > div .item p {
        color: #fff;
        position: relative;
        z-index: 2;
    }

    section.home-down .nav > div .item a {
        position: absolute;
        left: 25px;
        bottom: 40px;
        padding: 17px 50px;
        background-color: #bf9d7f;
        font-weight: 500;
        font-size: 15px;
        color: #fff;
        text-transform: uppercase;
        border-radius: 3px;
        display: inline-block;
        -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        z-index: 2;
    }

        section.home-down .nav > div .item a:hover {
            -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        }

        section.home-down .nav > div .item a:hover {
            background-color: #142333;
            color: #bf9d7f;
        }

    section.home-down .nav > div .item img {
        width: 100%;
        height: 100%;
        -o-object-fit: cover;
        object-fit: cover;
    }

    section.home-down .nav > div .item div {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
    }

    section.home-down .nav > div .item:nth-child(1) h5 {
        color: #142333;
    }

section.home-down .references > div {
    max-width: 1170px;
    position: relative;
    margin-left: auto;
    margin-right: auto;
}

    section.home-down .references > div:after {
        content: "";
        display: block;
        clear: both;
    }

@media (max-width: 1190px) {
    section.home-down .references > div {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.home-down .references > div > p {
    position: relative;
    float: left;
    font-weight: 600;
    font-size: 33px;
    width: 70%;
    color: #142333;
}

    section.home-down .references > div > p span {
        color: #bf9d7f;
    }

section.home-down .references > div > a {
    float: right;
    font-weight: 400;
    font-size: 16px;
    color: #959595;
    text-transform: capitalize;
    padding-top: 30px;
}

    section.home-down .references > div > a i {
        display: inline-block;
        margin-left: 7px;
        color: #bf9d7f;
        -webkit-transform: translateY(4px);
        transform: translateY(4px);
    }

    section.home-down .references > div > a:hover {
        text-decoration: underline;
    }

section.home-down .references > div .slider {
    float: left;
    width: 100%;
    margin-top: 45px;
}

    section.home-down .references > div .slider .slide {
        padding: 0 10px;
    }

        section.home-down .references > div .slider .slide a {
            border: 2px solid #bf9d7f;
            border-radius: 5px;
            display: block;
            position: relative;
            overflow: hidden;
            background-color: #142333;
        }

            section.home-down .references > div .slider .slide a img {
                display: block;
                height: 300px;
                width: 100%;
                -o-object-fit: cover;
                object-fit: cover;
            }

            section.home-down .references > div .slider .slide a h4 {
                padding: 15px;
                font-size: 23px;
                color: #fff;
            }

            section.home-down .references > div .slider .slide a p {
                padding: 0 15px;
                font-size: 17px;
                color: #bf9d7f;
            }

            section.home-down .references > div .slider .slide a span {
                display: block;
                padding: 15px 15px 25px;
                color: #fff;
                opacity: .8;
                text-transform: capitalize;
                font-size: 15px;
            }

                section.home-down .references > div .slider .slide a span i {
                    font-size: 11px;
                    display: inline-block;
                    -webkit-transform: translateY(1px);
                    transform: translateY(1px);
                    -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                    transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                }

                    section.home-down .references > div .slider .slide a span i:hover {
                        -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                        transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                    }

            section.home-down .references > div .slider .slide a:hover span i {
                margin-left: 7px;
            }

    section.home-down .references > div .slider .slick-dots {
        text-align: center;
        margin-top: 20px;
    }

        section.home-down .references > div .slider .slick-dots li {
            display: inline-block;
            width: 13px;
            height: 13px;
            border-radius: 50%;
            border: 1px solid #fff;
            background-color: #bf9d7f;
            margin: 0 4px;
        }

            section.home-down .references > div .slider .slick-dots li * {
                display: none;
            }

            section.home-down .references > div .slider .slick-dots li.slick-active {
                background-color: #142333;
                border-color: #bf9d7f;
            }

section.home-down .references > div .references-slider {
    float: left;
    width: 100%;
    margin-top: 45px;
}

    section.home-down .references > div .references-slider .slide {
        padding: 0 10px;
    }

        section.home-down .references > div .references-slider .slide a {
            height: 180px;
            border: 2px solid #a3a3a3;
            border-radius: 3px;
            display: block;
            position: relative;
            -webkit-transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
        }

            section.home-down .references > div .references-slider .slide a:hover {
                -webkit-transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
            }

            section.home-down .references > div .references-slider .slide a img {
                position: absolute;
                max-width: 75%;
                max-height: 98%;
                -webkit-filter: grayscale(100%);
                filter: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg"><filter id="filter"><feColorMatrix type="matrix" color-interpolation-filters="sRGB" values="0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0" /></filter></svg>#filter');
                filter: grayscale(100%);
                left: 50%;
                top: 50%;
                -webkit-transform: translate(-50%, -50%);
                transform: translate(-50%, -50%);
                -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
            }

                section.home-down .references > div .references-slider .slide a img:hover {
                    -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                    transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                }

            section.home-down .references > div .references-slider .slide a:hover {
                border: 2px solid #bf9d7f;
            }

                section.home-down .references > div .references-slider .slide a:hover img {
                    -webkit-filter: grayscale(0%);
                    filter: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg"><filter id="filter"><feColorMatrix type="matrix" color-interpolation-filters="sRGB" values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0" /></filter></svg>#filter');
                    filter: grayscale(0%);
                }

    section.home-down .references > div .references-slider .slick-dots {
        text-align: center;
        margin-top: 20px;
    }

        section.home-down .references > div .references-slider .slick-dots li {
            display: inline-block;
            width: 13px;
            height: 13px;
            border-radius: 50%;
            border: 1px solid #fff;
            background-color: #e3e3e3;
            margin: 0 4px;
        }

            section.home-down .references > div .references-slider .slick-dots li * {
                display: none;
            }

            section.home-down .references > div .references-slider .slick-dots li.slick-active {
                background-color: #fff;
                border-color: #bf9d7f;
            }

section.home-down .references.home-blog {
    background-color: #ecebe9;
    border-top: 1px solid #c8c8c8;
    border-bottom: 1px solid #c8c8c8;
    padding: 65px 0 40px;
    position: relative;
    overflow: hidden;
    margin-bottom: 60px;
}

    section.home-down .references.home-blog > p {
        position: absolute;
        width: 5000px;
        left: calc(50% - 2500px);
        text-align: center;
        font-weight: 600;
        font-size: 810px;
        color: #f0f0f0;
        text-transform: uppercase;
        -webkit-text-stroke: 1px #ddd;
    }

    section.home-down .references.home-blog .background {
        position: absolute;
        width: 100%;
        top: 300px;
    }

section.subpage-head {
    background-color: #142333;
/*    background-image: url(../img/subpage-head.webp);*/
    background-size: cover;
    padding: 150px 0 75px;
    text-align: center;
    overflow: hidden;
    position: relative;
    margin-bottom: 20px;
    border-bottom: 3px solid #bf9d7f;
}

    section.subpage-head h2 {
        font-weight: 600;
        font-size: 40px;
        color: #fff;
        text-transform: capitalize;
        position: relative;
        margin-bottom: 30px;
        z-index: 1;
    }

    section.subpage-head ul {
        position: relative;
        z-index: 1;
    }

        section.subpage-head ul li {
            display: inline-block;
            text-transform: capitalize;
        }

            section.subpage-head ul li a {
                display: block;
                font-weight: 300;
                font-size: 13px;
                color: #bf9d7f;
            }

                section.subpage-head ul li a i {
                    display: inline-block;
                    margin: 0 7px;
                    -webkit-transform: translateY(2px);
                    transform: translateY(2px);
                }

    section.subpage-head svg {
        position: absolute;
        bottom: -150px;
        left: 50%;
        margin-left: -325px;
    }

        section.subpage-head svg path, section.subpage-head svg polygon {
            fill-opacity: 0;
            stroke-width: 1px;
            -webkit-animation-name: cizim;
            animation-name: cizim;
            -webkit-animation-duration: 25s;
            animation-duration: 25s;
            -webkit-animation-fill-mode: forwards;
            animation-fill-mode: forwards;
            stroke-dasharray: 3319;
            stroke-dashoffset: 3319;
        }

section.home-services {
    border-top: 3px solid #bf9d7f;
    padding: 75px 0;
}

    section.home-services a h4 {
        color: #fff;
        font-size: 40px;
        text-transform: capitalize;
    }

    section.home-services a p {
        color: #bf9d7f;
        font-size: 19px;
        margin: 30px 0;
        padding: 0 100px;
        max-width: 1170px;
        position: relative;
        margin-left: auto;
        margin-right: auto;
    }

@media (max-width: 1190px) {
    section.home-services a p {
        line-height: 1.7;
        padding: 0;
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.home-services a i {
    font-size: 150px;
    color: transparent;
    -webkit-text-stroke: 1px #fff;
    display: inline-block;
    -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
    transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
}

    section.home-services a i:hover {
        -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
    }

section.home-services a:hover i {
    color: rgba(255, 255, 255, .1);
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
}

section.about {
    padding: 60px 0;
}

    section.about .grid {
        max-width: 1170px;
        position: relative;
        margin-left: auto;
        margin-right: auto;
        z-index: 1;
    }

@media (max-width: 1190px) {
    section.about .grid {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.about .grid > div > div {
    margin: 0 -2%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

    section.about .grid > div > div > * {
        width: 46%;
        margin: 0 2% 0;
    }

section.about .grid > div .slogantext {
    font-size: 35px;
    margin-bottom: 30px;
    padding: 70px 100px 0 0;
}

    section.about .grid > div .slogantext:before {
        left: 0;
        top: -55px;
    }

section.about .grid > div .text {
    font-weight: 400;
    font-size: 17px;
    color: #20363c;
    line-height: 1.5;
    text-align: justify;
}

section.about .grid > div img {
    width: 100%;
    height: 435px;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 3px;
}

section.about .background {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

section.whyus {
    background-color: #ecebe9;
    padding: 60px 0;
    border: 1px solid #c8c8c8;
    text-align: center;
    max-width: 1700px;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    overflow: hidden;
}

@media (max-width: 1720px) {
    section.whyus {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.whyus .grid {
    max-width: 1170px;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    z-index: 2;
}

@media (max-width: 1190px) {
    section.whyus .grid {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.whyus .grid h5:before {
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
}

section.whyus .grid h5 + p {
    color: #20363c;
    padding: 0 70px;
}

section.whyus .grid > div {
    margin-top: 30px;
}

    section.whyus .grid > div > div {
        margin: 0 -0.5%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.whyus .grid > div > div > * {
            width: 24%;
            margin: 0 0.5% 0;
        }

    section.whyus .grid > div a {
        background-color: #142333;
        border: 2px solid #bf9d7f;
        -webkit-box-shadow: inset 0.122px 3px 4px 0 rgba(8, 18, 29, .04);
        box-shadow: inset 0.122px 3px 4px 0 rgba(8, 18, 29, .04);
        border-radius: 3px;
        padding: 60px 20px;
        -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
    }

        section.whyus .grid > div a:hover {
            -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
        }

        section.whyus .grid > div a i {
            font-size: 80px;
            color: #bf9d7f;
            margin-bottom: 25px;
        }

        section.whyus .grid > div a p {
            font-weight: 600;
            font-size: 23px;
            color: #fff;
            text-transform: capitalize;
            height: 85px;
        }

        section.whyus .grid > div a span {
            font-weight: 400;
            font-size: 16px;
            color: #fff;
            display: block;
            height: 175px;
            text-align: justify;
            line-height: 1.5
        }

        section.whyus .grid > div a:hover {
            -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
        }

section.whyus .background {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

    section.whyus .background svg {
        position: absolute;
        left: 50%;
        margin-left: -755px;
        z-index: 2;
    }

    section.whyus .background div {
        width: 700px;
        height: 430px;
        background-image: url(../img/gallery-background.png);
        position: absolute;
        right: 20px;
        bottom: 40px;
        z-index: 1;
    }

section.awards {
    padding: 60px 0;
}

    section.awards .grid {
        max-width: 1170px;
        position: relative;
        margin-left: auto;
        margin-right: auto;
    }

@media (max-width: 1190px) {
    section.awards .grid {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.awards .grid h5 {
    text-align: center;
    margin-bottom: 15px;
}

    section.awards .grid h5:before {
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
        right: auto;
    }

section.awards .grid > div {
    margin-top: 30px;
}

    section.awards .grid > div:after {
        content: "";
        display: block;
        clear: both;
    }

    section.awards .grid > div > div {
        float: left;
    }

        section.awards .grid > div > div img {
            width: 100%;
        }

        section.awards .grid > div > div .award-text {
            font-weight: 400;
            font-size: 27px;
            color: #142333;
            text-align: center;
            margin: 46px 0;
        }

            section.awards .grid > div > div .award-text span {
                font-weight: 600;
                display: block;
            }

        section.awards .grid > div > div .slogantext {
            font-size: 35px;
            padding: 0;
        }

            section.awards .grid > div > div .slogantext:before {
                top: -140px;
                left: 0;
            }

        section.awards .grid > div > div .alt-text {
            font-weight: 400;
            font-size: 17px;
            color: #20363c;
            text-align: justify;
        }

        section.awards .grid > div > div:nth-child(1) {
            width: 32%;
            padding-right: 75px;
        }

        section.awards .grid > div > div:nth-child(2) {
            width: 68%;
        }

section.vision {
    max-width: 1700px;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    background-color: #142333;
    padding: 70px 0;
    border-radius: 3px;
}

@media (max-width: 1720px) {
    section.vision {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.vision > div {
    max-width: 1170px;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    color: #fff;
}

@media (max-width: 1190px) {
    section.vision > div {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.vision > div h5 {
    color: #fff;
}

    section.vision > div h5:before {
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
        right: auto;
    }

section.vision > div h4 {
    font-weight: 600;
    font-size: 24px;
}

    section.vision > div h4 span {
        color: #bf9d7f;
    }

    section.vision > div h4 i {
        font-size: 90px;
        display: inline-block;
        -webkit-transform: translateY(30px);
        transform: translateY(30px);
        color: #bf9d7f;
        margin: 0 10px;
    }

section.founder {
    padding: 60px 0;
}

    section.founder .grid {
        max-width: 1170px;
        position: relative;
        margin-left: auto;
        margin-right: auto;
        padding: 60px 400px 60px 75px;
        background-color: #ecebe9;
        border-radius: 3px;
    /*    background-image: url(../img/founder.webp);*/
        background-position: right center;
        background-size: cover;
    }

@media (max-width: 1190px) {
    section.founder .grid {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.founder .grid h5 {
    font-size: 30px;
}

section.founder .grid h3 {
    font-weight: 600;
    font-size: 23px;
    color: #142333;
}

section.founder .grid p {
    font-weight: 400;
    font-size: 17px;
    color: #142333;
    padding: 7px 0 20px;
}

section.founder .grid a {
    font-weight: 300;
    font-size: 23px;
    color: #bf9d7f;
    -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
    transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
}

    section.founder .grid a:hover {
        -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
    }

    section.founder .grid a i {
        display: inline-block;
        -webkit-transform: translateY(4px);
        transform: translateY(4px);
        margin-right: 5px;
    }

    section.founder .grid a:hover {
        color: #20363c;
    }

section.docs {
    padding-bottom: 60px;
}

    section.docs > div {
        max-width: 1170px;
        position: relative;
        margin-left: auto;
        margin-right: auto;
        text-align: center;
    }

@media (max-width: 1190px) {
    section.docs > div {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.docs > div h5:before {
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    right: auto
}

section.docs > div > div > div {
    margin: 0 -0.5%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

    section.docs > div > div > div > * {
        width: 24%;
        margin: 0 0.5% 15px;
    }

section.docs > div > div a {
    background-color: #bf9d7f;
    border: 2px solid #ad8d71;
    border-radius: 3px;
    padding: 60px 20px;
    -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
    transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
}

    section.docs > div > div a:hover {
        -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
    }

    section.docs > div > div a i {
        font-size: 80px;
        color: #fff;
        margin-bottom: 25px;
    }

    section.docs > div > div a p {
        font-weight: 600;
        font-size: 23px;
        color: #fff;
        text-transform: capitalize;
        margin-bottom: 15px;
    }

    section.docs > div > div a span {
        font-weight: 400;
        font-size: 16px;
        color: #fff;
        display: block;
        padding: 0 20px 30px;
    }

    section.docs > div > div a button {
        padding: 0 15px;
        height: 50px;
        background-color: #142333;
        border-radius: 3px;
        color: #fff;
        font-weight: 500;
        font-size: 14px;
        color: #fff;
        text-transform: capitalize;
        border: 1px solid transparent;
        -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
    }

        section.docs > div > div a button:hover {
            -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        }

        section.docs > div > div a button:hover {
            border-color: #142333;
            background-color: transparent;
            color: #142333;
        }

    section.docs > div > div a:hover {
        -webkit-transform: translateY(-10px);
        transform: translateY(-10px);
    }

section.subpage {
    text-align: center;
    padding: 50px 0;
}

    section.subpage > div {
        max-width: 1170px;
        position: relative;
        margin-left: auto;
        margin-right: auto;
    }

@media (max-width: 1190px) {
    section.subpage > div {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.subpage > div h4 {
    font-weight: 600;
    font-size: 33px;
    color: #142333;
    padding: 0 210px;
    margin: 0 0 30px;
}

    section.subpage > div h4 span {
        color: #bf9d7f;
    }

    section.subpage > div h4 + p {
        font-weight: 400;
        font-size: 17px;
        color: #20363c;
        margin-top: 25px;
        padding: 0 25px;
    }


section.subpage > div ul {
    margin: 0 0 30px;
}

    section.subpage > div ul li {
        display: inline-block;
    }

        section.subpage > div ul li a {
            display: block;
            font-weight: 600;
            font-size: 14px;
            color: #142333;
            padding: 10px 40px;
            background-color: #fff;
            border: 1px solid #bf9d7f;
            border-radius: 3px;
            margin: 0 2px;
            -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            text-transform: capitalize;
        }

            section.subpage > div ul li a:hover {
                -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            }

            section.subpage > div ul li a sup {
                line-height: 0;
            }

        section.subpage > div ul li:hover a {
            background-color: #142333;
            color: #fff;
        }

        section.subpage > div ul li.active a {
            background-color: #bf9d7f;
            color: #fff;
            border-color: #ad8d71;
        }

section.subpage > div .list {
    text-align: left;
}

    section.subpage > div .list > div {
        margin: 0 -0.5%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.subpage > div .list > div > * {
            width: 32.33333%;
            margin: 0 0.5% 15px;
        }

    section.subpage > div .list a {
        overflow: hidden;
        position: relative;
        border-radius: 3px;
    }

        section.subpage > div .list a img {
            width: 100%;
            height: 280px;
            -o-object-fit: cover;
            object-fit: cover;
            -webkit-transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
            display: block;
        }

            section.subpage > div .list a img:hover {
                -webkit-transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.6s cubic-bezier(0.65, 0, 0.35, 1);
            }

        section.subpage > div .list a h3 {
            font-weight: 600;
            font-size: 19px;
            text-shadow: 0 2px 3px rgba(36, 48, 59, .4);
            -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        }

            section.subpage > div .list a h3:hover {
                -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            }

            section.subpage > div .list a h3:before {
                content: "";
                position: absolute;
                width: 130px;
                height: 3px;
                background-color: #bf9d7f;
                left: 0;
                bottom: 0;
                -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            }

                section.subpage > div .list a h3:before:hover {
                    -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
                    transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
                }
        section.subpage > div .list a h2 {
            font-weight: 600;
            font-size: 19px;
            text-shadow: 0 2px 3px rgba(36, 48, 59, .4);
            -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        }

            section.subpage > div .list a h2:hover {
                -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            }

            section.subpage > div .list a h2:before {
                content: "";
                position: absolute;
                width: 130px;
                height: 3px;
                background-color: #bf9d7f;
                left: 0;
                bottom: 0;
                -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            }

                section.subpage > div .list a h2:before:hover {
                    -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
                    transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
                }
        section.subpage > div .list a:hover img {
            opacity: .5;
            -webkit-transform: scale(1.1);
            transform: scale(1.1);
        }

section.humanr ul {
    margin-top: 50px !important;
}

    section.humanr ul li {
        display: block !important;
        border: 1px solid #142333;
        border-radius: 5px;
        margin-bottom: 15px;
        text-align: left;
        padding: 20px;
    }

        section.humanr ul li div {
            position: relative;
        }

            section.humanr ul li div h3 {
                font-weight: 500;
                font-size: 25px;
                color: #142333;
            }

            section.humanr ul li div p {
                font-weight: 300;
                font-size: 16px;
                color: #8d8d8d;
            }

            section.humanr ul li div span {
                display: inline-block;
                position: absolute;
                right: 0;
                top: 50%;
                -webkit-transform: translateY(-50%);
                transform: translateY(-50%);
                background-color: #142333;
                border: 1px solid #bf9d7f;
                border-radius: 3px;
                padding: 8px 30px;
                font-size: 15px;
                color: #fff;
                -webkit-transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
            }

                section.humanr ul li div span:hover {
                    -webkit-transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
                    transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
                }

                section.humanr ul li div span:hover {
                    background-color: #bf9d7f;
                    color: #142333;
                }

        section.humanr ul li form {
        }

section.gallery-page > div .list a {
    background-color: #142333;
}

    section.gallery-page > div .list a img {
        opacity: .8;
    }

    section.gallery-page > div .list a h3 {
        position: absolute;
        z-index: 1;
        left: 14px;
        right: 20px;
        bottom: -100px;
        color: #fff;
        padding-bottom: 20px;
    }
    section.gallery-page > div .list a h2 {
        position: absolute;
        z-index: 1;
        left: 14px;
        right: 20px;
        bottom: -100px;
        color: #fff;
        padding-bottom: 20px;
    }

    section.gallery-page > div .list a div {
        position: absolute;
        left: 50%;
        top: 50%;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        z-index: 2;
        width: 110px;
        height: 110px;
        border-radius: 50%;
        border: 1px solid #fff;
    }

        section.gallery-page > div .list a div div {
            width: 70px;
            height: 70px;
            background-color: rgba(20, 35, 51, .42);
        }

            section.gallery-page > div .list a div div i {
                font-size: 30px;
                color: #bf9d7f;
                position: absolute;
                line-height: 0;
                left: 50%;
                top: 50%;
                -webkit-transform: translate(-50%, -50%);
                transform: translate(-50%, -50%);
            }

    section.gallery-page > div .list a:hover h3 {
        bottom: 25px;
    }
    section.gallery-page > div .list a:hover h2 {
        bottom: 25px;
    }
section.projects-page > div .list a {
    background-color: #142333;
    color: #fff;
}

    section.projects-page > div .list a:before {
        content: "";
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: transparent;
        background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(#142333));
        background: linear-gradient(transparent, #142333);
        z-index: 1;
    }

    section.projects-page > div .list a img {
        opacity: .75;
    }

    section.projects-page > div .list a h3 {
        position: absolute;
        left: 25px;
        right: 25px;
        bottom: 65px;
        z-index: 2;
    }

        section.projects-page > div .list a h3:before {
            bottom: -35px;
        }
    section.projects-page > div .list a h2 {
        position: absolute;
        left: 25px;
        right: 25px;
        bottom: 65px;
        z-index: 2;
    }

        section.projects-page > div .list a h2:before {
            bottom: -35px;
        }
    section.projects-page > div .list a p {
        position: absolute;
        left: 25px;
        right: 25px;
        bottom: 40px;
        z-index: 2;
        font-weight: 400;
        font-size: 17px;
    }

    section.projects-page > div .list a span {
        position: absolute;
        left: 25px;
        right: 25px;
        bottom: 40px;
        z-index: 2;
        font-weight: 300;
        font-size: 15px;
    }

    section.projects-page > div .list a:hover:before {
        background: transparent;
        background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(#bf9d7f));
        background: linear-gradient(transparent, #bf9d7f);
    }

    section.projects-page > div .list a:hover h3 {
        bottom: 80px;
    }

        section.projects-page > div .list a:hover h3:before {
            background-color: #fff;
            bottom: -55px;
        }
    section.projects-page > div .list a:hover h2 {
        bottom: 80px;
    }

        section.projects-page > div .list a:hover h2:before {
            background-color: #fff;
            bottom: -55px;
        }
section.projects-page #map {
    height: calc(100vh - 480px);
    min-height: 650px;
    max-width: none;
    margin: 25px 0;
}

    section.projects-page #map .haritaacilan {
        min-width: 320px;
        padding: 10px;
        text-align: left;
    }

        section.projects-page #map .haritaacilan:after {
            content: "";
            display: block;
            clear: both;
        }

        section.projects-page #map .haritaacilan div {
            width: 130px;
            height: 130px;
            border-radius: 3px;
            background-size: cover;
            float: left;
            margin-right: 10px;
        }

        section.projects-page #map .haritaacilan h1 {
            padding: 25px 0 0 0;
            font-weight: 600;
            font-size: 19px;
            color: #142333;
        }

        section.projects-page #map .haritaacilan p {
            font-weight: 400;
            font-size: 13px;
            color: #bf9d7f;
            margin: 5px 0 25px 0;
        }

        section.projects-page #map .haritaacilan a {
            font-weight: 400;
            font-size: 13px;
            color: #959595;
            text-transform: capitalize;
        }

            section.projects-page #map .haritaacilan a i {
                color: #bf9d7f;
                margin: 0 0 0 10px;
                display: inline-block;
                -webkit-transform: translateY(3px);
                transform: translateY(3px);
            }

section.map-page ul {
    margin-top: 30px !important;
}

section.blog-page > div .list a img {
    height: 400px;
}

section.blog-page > div .list a h3 {
    bottom: 120px;
}

    section.blog-page > div .list a h3:before {
        bottom: -10px;
    }
section.blog-page > div .list a h2 {
    bottom: 120px;
}

    section.blog-page > div .list a h2:before {
        bottom: -10px;
    }
section.blog-page > div .list a p {
    position: absolute;
    left: 25px;
    right: 25px;
    bottom: 50px;
    z-index: 2;
    font-weight: 400;
    font-size: 17px;
}

section.blog-page > div .list a:hover h3 {
    bottom: 110px;
}

    section.blog-page > div .list a:hover h3:before {
        bottom: -95px;
    }
section.blog-page > div .list a:hover h2 {
    bottom: 110px;
}

    section.blog-page > div .list a:hover h2:before {
        bottom: -95px;
    }
section.project-detail figure img {
    max-width: 100%;
    margin: 40px 0
}

.project-div p {
    margin: 20px 0;
    line-height: 1.5
}

.project-div * {
    color: #132333
}

section.project-detail > div {
    max-width: 1170px;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    padding: 30px 0;
}

@media (max-width: 1190px) {
    section.project-detail > div {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.project-detail > div .title {
    margin-bottom: 30px;
    position: relative;
    padding-right: 150px;
}

    section.project-detail > div .title h1 {
        font-weight: 600;
        font-size: 35px;
        color: #142333;
    }

        section.project-detail > div .title h1 span {
            color: #bf9d7f;
        }

    section.project-detail > div .title p {
        width: 100%;
        font-weight: 400;
        font-size: 17px;
        color: #20363c;
        margin-top: 5px;
    }

        section.project-detail > div .title p span {
            font-weight: 600;
        }

    section.project-detail > div .title a {
        position: absolute;
        right: 0;
        top: 10px;
        font-weight: 500;
        font-size: 15px;
        color: #142333;
        text-transform: capitalize;
        border: 1px solid #bf9d7f;
        padding: 11px 20px;
        -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
    }

        section.project-detail > div .title a:hover {
            -webkit-transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.5s cubic-bezier(0.65, 0, 0.35, 1);
        }

        section.project-detail > div .title a i {
            color: #bf9d7f;
            display: inline-block;
            -webkit-transform: translateY(2px);
            transform: translateY(2px);
            margin-left: 10px;
        }

        section.project-detail > div .title a:hover {
            background-color: #142333;
            color: #fff;
        }

    section.project-detail > div .title + p {
        font-weight: 400;
        font-size: 17px;
        color: #20363c;
        text-align: justify;
        margin-bottom: 35px;
    }

section.project-detail > div .count {
    margin-bottom: 25px;
}

    section.project-detail > div .count p {
        font-weight: 600;
        font-size: 31px;
        color: #142333;
        margin-bottom: 18px;
    }

        section.project-detail > div .count p span {
            color: #bf9d7f;
        }

    section.project-detail > div .count > div {
        text-align: center;
    }

        section.project-detail > div .count > div > div {
            margin: 0 -0.5%;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-wrap: wrap;
            flex-wrap: wrap;
        }

            section.project-detail > div .count > div > div > * {
                width: 24%;
                margin: 0 0.5% 0;
            }

        section.project-detail > div .count > div a {
            background-color: #bf9d7f;
            border-radius: 3px;
            padding: 30px 10px;
        }

            section.project-detail > div .count > div a p {
                font-weight: 600;
                font-size: 65px;
                color: #fff;
            }

            section.project-detail > div .count > div a span {
                font-weight: 300;
                font-size: 25px;
                color: #fff;
                text-transform: uppercase;
            }

section.project-detail > div .before-after {
    margin-bottom: 30px;
}

    section.project-detail > div .before-after > div {
        height: 600px;
        border-radius: 3px;
        overflow: hidden;
    }

        section.project-detail > div .before-after > div #comparison figure div {
            background-size: 1170px;
        }

section.project-detail > div .slider {
    margin-bottom: 40px;
}

    section.project-detail > div .slider a {
        display: block;
        position: relative;
    }

        section.project-detail > div .slider a img {
            display: block;
            -o-object-fit: cover;
            object-fit: cover;
            width: 100%;
            height: 360px;
            border-radius: 3px;
        }

        section.project-detail > div .slider a i {
            position: absolute;
            right: 30px;
            bottom: 30px;
            font-size: 35px;
            color: #fff;
            line-height: 0;
            -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        }

            section.project-detail > div .slider a i:hover {
                -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
            }

        section.project-detail > div .slider a:hover i {
            -webkit-transform: scale(1.2);
            transform: scale(1.2);
        }

    section.project-detail > div .slider .slick-arrow {
        position: absolute;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        z-index: 2;
        width: 50px;
        height: 50px;
        border-radius: 50px;
        border: 1px solid #bf9d7f;
        -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        opacity: .7;
        background-color: #142333;
        color: #fff;
        cursor: pointer;
    }

        section.project-detail > div .slider .slick-arrow:hover {
            -webkit-transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
        }

        section.project-detail > div .slider .slick-arrow:before {
            position: absolute;
            left: 50%;
            top: 50%;
            -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
        }

        section.project-detail > div .slider .slick-arrow.slick-prev {
            left: 20px;
            -webkit-transform: rotate(180deg) translateY(50%);
            transform: rotate(180deg) translateY(50%);
        }

        section.project-detail > div .slider .slick-arrow.slick-next {
            right: 20px;
        }

        section.project-detail > div .slider .slick-arrow:hover {
            opacity: 1;
        }

    section.project-detail > div .slider .slick-dots {
        text-align: center;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 20px;
    }

        section.project-detail > div .slider .slick-dots li {
            display: inline-block;
            width: 13px;
            height: 13px;
            border-radius: 15px;
            border: 1px solid #142333;
            margin: 0 2px;
        }

            section.project-detail > div .slider .slick-dots li * {
                display: none;
            }

            section.project-detail > div .slider .slick-dots li.slick-active {
                background-color: #142333;
            }

section.project-detail > div .photos a {
    display: block;
    position: relative;
    cursor: pointer;
}

    section.project-detail > div .photos a img {
        display: block;
        -o-object-fit: cover;
        object-fit: cover;
        width: 100%;
        height: 460px;
        border-radius: 3px;
    }

    section.project-detail > div .photos a i {
        position: absolute;
        right: 30px;
        bottom: 30px;
        font-size: 35px;
        color: #fff;
        line-height: 0;
        -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
    }

        section.project-detail > div .photos a i:hover {
            -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        }

    section.project-detail > div .photos a:hover i {
        -webkit-transform: scale(1.2);
        transform: scale(1.2);
    }

section.project-detail > div .photos > a img {
    height: 930px;
    margin-bottom: 25px;
}

section.project-detail > div .photos > div > div {
    margin: 0 -0.5%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

    section.project-detail > div .photos > div > div > * {
        width: 49%;
        margin: 0 0.5% 12px;
    }

section.contact {
    padding-bottom: 60px;
}

    section.contact > div {
        max-width: 1170px;
        position: relative;
        margin-left: auto;
        margin-right: auto;
    }

@media (max-width: 1190px) {
    section.contact > div {
        margin-left: 10px;
        margin-right: 10px;
    }
}

section.contact > div > div > div {
    margin: 0 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

    section.contact > div > div > div > * {
        width: 33.33333%;
        margin: 0 0 0;
    }

section.contact > div > div > div {
    border-radius: 3px;
    overflow: hidden;
}

    section.contact > div > div > div > div {
        background-color: #142333;
        padding: 60px;
        text-align: center;
        position: relative;
    }

        section.contact > div > div > div > div i {
            font-size: 45px;
            color: #bf9d7f;
        }

        section.contact > div > div > div > div p {
            font-weight: 600;
            font-size: 18px;
            color: #bf9d7f;
            margin-bottom: 20px;
        }

        section.contact > div > div > div > div a {
            font-weight: 300;
            font-size: 17px;
            color: #fff;
            display: block;
            margin-bottom: 10px;
        }

            section.contact > div > div > div > div a i {
                font-size: 16px;
                color: #354454;
                display: inline-block;
                -webkit-transform: translateY(3px);
                transform: translateY(3px);
                -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                margin-right: 3px;
            }

                section.contact > div > div > div > div a i:hover {
                    -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                    transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
                }

            section.contact > div > div > div > div a:hover i {
                color: #fff;
            }

        section.contact > div > div > div > div + div:before {
            content: "";
            position: absolute;
            width: 1px;
            height: 130px;
            left: 0;
            top: 50%;
            -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
            background-color: #2c3a4f;
        }

section.docs-page {
    padding: 60px 0;
}

section.references-page {
    padding: 60px 0;
}

    section.references-page > div > div a {
        height: 180px;
        border: 2px solid #bf9d7f;
        background-color: #fff;
        border-radius: 3px;
        display: block;
        position: relative;
    }

        section.references-page > div > div a img {
            position: absolute;
            width: 75%;
            left: 50%;
            top: 50%;
            -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
        }

        section.references-page > div > div a:hover {
            -webkit-transform: translate(0);
            transform: translate(0);
        }

section.detail-video {
    margin-bottom: 50px;
}

    section.detail-video h4 {
        margin-bottom: 35px;
    }

@-webkit-keyframes cizim {
    7% {
        fill-opacity: 0;
        stroke-width: 1px;
    }

    35% {
        fill-opacity: 0;
        stroke-width: 1;
    }

    100% {
        stroke-width: 1px;
        fill-opacity: 0;
        stroke-dashoffset: 0;
    }
}

@keyframes cizim {
    7% {
        fill-opacity: 0;
        stroke-width: 1px;
    }

    35% {
        fill-opacity: 0;
        stroke-width: 1;
    }

    100% {
        stroke-width: 1px;
        fill-opacity: 0;
        stroke-dashoffset: 0;
    }
}

footer {
    background-color: #12202f;
    padding: 60px 0 30px;
    position: relative;
  /*  background-image: url(../img/footer-background.webp);*/
    background-size: cover;
}

    footer .footer {
        max-width: 1170px;
        position: relative;
        margin-left: auto;
        margin-right: auto;
    }

@media (max-width: 1190px) {
    footer .footer {
        margin-left: 10px;
        margin-right: 10px;
    }
}

footer .footer > div {
    margin: 0 -1.5%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

    footer .footer > div > * {
        width: 17%;
        margin: 0 1.5% 0;
    }

    footer .footer > div > div:last-child {
        text-align: right;
    }

        footer .footer > div > div:last-child h6:before {
            right: 0;
            left: auto;
        }

footer .footer h6 {
    font-weight: 600;
    font-size: 20px;
    color: #bf9d7f;
    text-transform: uppercase;
    padding-bottom: 25px;
    margin-bottom: 15px;
    position: relative;
}

    footer .footer h6:before {
        content: "";
        width: 47px;
        height: 2px;
        background-color: #4e535f;
        bottom: 0;
        left: 0;
        position: absolute;
    }

footer .footer ul.social i, footer .footer ul.social svg {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #8e8e8e;
    display: inline-block;
    margin-right: 7px;
    font-size: 12px;
    position: relative;
    -webkit-transform: translateY(5px);
    transform: translateY(5px)
}

    footer .footer ul.social svg * {
        fill: #142333;
        width: 10px;
        position: absolute;
        left: 50%;
        top: 50%;
        -webkit-transform: scale(0.7) translate(90px, 120px);
        transform: scale(0.7) translate(90px, 120px);
    }

    footer .footer ul.social i:before {
        color: #142333;
        position: absolute;
        left: 50%;
        top: 50%;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
    }

footer .footer ul li a, footer .footer address {
    display: block;
    font-weight: 500;
    font-size: 15px;
    color: #8e8e8e;
    line-height: 1.7;
    -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
    transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
}

    footer .footer ul li a:hover, footer .footer address:hover {
        -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
    }

    footer .footer ul li a i, footer .footer address i {
        -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
    }

        footer .footer ul li a i:hover, footer .footer address i:hover {
            -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        }

    footer .footer ul li a span, footer .footer address span {
        font-weight: 700;
    }

    footer .footer ul li a:hover, footer .footer address:hover {
        padding-left: 5px;
    }

    footer .footer ul li a.all, footer .footer address.all {
        color: #8c7766;
    }

        footer .footer ul li a.all i, footer .footer address.all i {
            display: inline-block;
            -webkit-transform: translateY(3px);
            transform: translateY(3px);
        }

        footer .footer ul li a.all:hover i, footer .footer address.all:hover i {
            margin-left: 5px;
        }

footer .footer img {
    width: 120px;
    margin-top: 10px;
}

footer .bottom {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid #273544;
}

    footer .bottom > div {
        max-width: 1170px;
        position: relative;
        margin-left: auto;
        margin-right: auto;
    }

@media (max-width: 1190px) {
    footer .bottom > div {
        margin-left: 10px;
        margin-right: 10px;
    }
}

footer .bottom > div:after {
    content: "";
    display: block;
    clear: both;
}

footer .bottom > div * {
    font-weight: 400;
    font-size: 16px;
    color: #6f7274;
}

footer .bottom > div p {
    display: inline;
}

footer .bottom > div a {
    color: #4e535f;
}

    footer .bottom > div a:hover {
        color: #bf9d7f;
    }

footer .bottom > div .left {
    float: left;
}

footer .bottom > div .right {
    float: right;
}

    footer .bottom > div .right a {
        color: #bf9d7f;
    }

.whatsapp {
    position: fixed;
    z-index: 500;
    right: 0;
    top: 63%;
    padding: 10px 20px 10px 25px;
    border-radius: 60px 0 0 60px;
    background-color: #bf9d7f;
    -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
    transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
}

    .whatsapp:hover {
        -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
    }

    .whatsapp i {
        font-size: 40px;
        color: #fff;
        line-height: 0;
    }

    .whatsapp:hover {
        padding-right: 35px;
    }

.social-fixed {
    position: fixed;
    z-index: 500;
    left: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}

    .social-fixed a {
        padding: 12px 10px 10px;
        border-radius: 0 60px 60px 0;
        background-color: #bf9d7f;
        -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        display: block;
        margin: 5px 0;
    }

        .social-fixed a:hover {
            -webkit-transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
            transition: all 0.2s cubic-bezier(0.65, 0, 0.35, 1);
        }

        .social-fixed a i {
            font-size: 30px;
            color: #fff;
            line-height: 0;
        }

        .social-fixed a:hover {
            padding-left: 25px;
            margin-left: -15px;
            -webkit-transform: translateX(15px);
            transform: translateX(15px);
        }

@media (min-width: 1200px) {
    header > div.bottom > ul li:hover:not(.lang) ul {
        display: block;
    }
}

@media (max-width: 1200px) {
    header > div.top {
        text-align: center;
    }

        header > div.top > div {
            margin: 0 0;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-wrap: wrap;
            flex-wrap: wrap;
        }

            header > div.top > div > * {
                width: 100% !important;
                margin: 0 0 0;
            }

        header > div.top a {
            margin: 0 5px;
            display: inline-block;
        }

        header > div.top .social {
            display: none;
        }

    header > div.bottom > ul.menu {
        display: none;
        width: 100%;
        text-align: center;
        margin-top: 10px;
    }

        header > div.bottom > ul.menu li {
            display: block;
        }

            header > div.bottom > ul.menu li a {
                border-top: 1px solid rgba(255, 255, 255, .1);
                padding: 15px 0;
            }

            header > div.bottom > ul.menu li ul {
                position: relative;
                padding: 0;
            }

    header > div.bottom > button {
        display: block;
    }

    section.gallery {
        padding: 35px 0;
    }

        section.gallery .grid .gallery a h3 {
            left: 20px;
        }
        section.gallery .grid .gallery a h2 {
            left: 20px;
        }
    section.about .grid > div > div {
        margin: 0 -1%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.about .grid > div > div > * {
            width: 48%;
            margin: 0 1% 0;
        }

    section.about .grid > div .slogantext {
        padding: 60px 0 0;
    }

    section.whyus .grid > div > div {
        margin: 0 -0.5%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.whyus .grid > div > div > * {
            width: 49%;
            margin: 0 0.5% 10px;
        }

    section.docs > div > div > div {
        margin: 0 -0.5%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.docs > div > div > div > * {
            width: 32.33333%;
            margin: 0 0.5% 15px;
        }

    section.subpage > div .list a h3 {
        font-size: 16px;
    }
    section.subpage > div .list a h2 {
        font-size: 16px;
    }
    section.subpage > div .list a span {
        font-size: 13px;
    }

    footer .footer > div {
        margin: 0 -1%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        footer .footer > div > * {
            width: 48%;
            margin: 0 1% 40px;
        }

        footer .footer > div > div:nth-child(1) {
            display: none;
        }

        footer .footer > div > div:last-child {
            text-align: left;
        }

            footer .footer > div > div:last-child h6:before {
                right: auto;
                left: 0;
            }

    footer .bottom > div * {
        width: 100%;
        text-align: center;
    }
}

@media (max-width: 900px) {
    section.projects > div .projects > div {
        margin: 0 0;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.projects > div .projects > div > * {
            width: 100%;
            margin: 0 0 0;
        }

    section.gallery .grid .gallery {
        height: 750px;
    }

        section.gallery .grid .gallery a h3 {
            font-size: 16px;
        }
        section.gallery .grid .gallery a h2 {
            font-size: 16px;
        }
        section.gallery .grid .gallery a:nth-child(1) {
            left: 33.33%;
            width: 66.66%;
            height: 50%;
        }

        section.gallery .grid .gallery a:nth-child(2) {
            width: 33.33%;
        }

        section.gallery .grid .gallery a:nth-child(3) {
            width: 33.33%;
        }

        section.gallery .grid .gallery a:nth-child(4) {
            top: 50%;
            left: 33.33%;
            width: 33.33%;
            height: 30%;
        }

        section.gallery .grid .gallery a:nth-child(5) {
            top: 50%;
            width: 33.33%;
            height: 30%;
        }

        section.gallery .grid .gallery a:nth-child(6) {
            left: 33.33%;
            top: 80%;
            height: 20%;
        }

        section.gallery .grid .gallery a:nth-child(7) {
            top: 80%;
            width: 41.67%;
            height: 20%;
        }

    section.home-down .nav > div > div {
        margin: 0 0;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.home-down .nav > div > div > * {
            width: 100%;
            margin: 0 0 10px;
        }

    section.home-down .nav > div .item {
        height: 400px;
    }

    section.home-down > div .references > p {
        width: 100%;
    }

    section.home-down > div .references > a {
        float: left;
        padding-top: 10px;
    }

    section.about .grid > div > div {
        margin: 0 0;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.about .grid > div > div > * {
            width: 100%;
            margin: 0 0 10px;
        }

    section.about .grid > div .slogantext {
        font-size: 30px;
        padding-top: 0;
    }

        section.about .grid > div .slogantext:before {
            font-size: 150px;
        }

    section.about .grid > div .text {
        font-size: 15px;
    }

    section.whyus {
        padding: 30px 0;
    }

        section.whyus .grid h5 + p {
            padding: 0;
        }

        section.whyus .grid > div > div {
            margin: 0 -0.5%;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-wrap: wrap;
            flex-wrap: wrap;
        }

            section.whyus .grid > div > div > * {
                width: 99%;
                margin: 0 0.5% 10px;
            }

    section.vision, section.founder {
        padding: 40px 0;
    }

    section.humanr ul li div {
        text-align: center;
    }

        section.humanr ul li div span {
            position: relative;
            -webkit-transform: translate(0);
            transform: translate(0);
            display: block;
            margin-top: 10px;
        }

    section.awards .grid > div > div {
        width: 100% !important;
        padding: 0 !important;
        margin-bottom: 10px;
    }

        section.awards .grid > div > div:nth-child(1) img {
            width: 50%;
            margin: 0 25%;
        }

    section.founder .grid {
        padding: 20px 300px 20px 20px;
    }

    section.subpage > div ul {
        margin: 20px 0 10px;
    }

        section.subpage > div ul li a {
            margin-bottom: 10px;
        }

    section.subpage > div h4 {
        padding: 0;
    }

    section.gallery-page > div .list a img {
        height: 200px;
    }

    section.projects-page > div .list > div {
        margin: 0 -1%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.projects-page > div .list > div > * {
            width: 48%;
            margin: 0 1% 15px;
        }

    section.contact > div > div > div {
        margin: 0 0;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.contact > div > div > div > * {
            width: 100%;
            margin: 0 0 0;
        }

        section.contact > div > div > div > div {
            padding: 20px;
        }

            section.contact > div > div > div > div + div:before {
                left: 50%;
                -webkit-transform: translateX(-50%);
                transform: translateX(-50%);
                top: 0;
                width: 230px;
                height: 1px;
            }
}

@media (max-width: 750px) {
    .slogantext:before {
        left: 0;
        top: -30px;
    }

    section.slider a div {
        padding: 22vh 0 0;
    }

        section.slider a div h2 {
            font-size: 45px;
            margin-bottom: 40px;
        }

        section.slider a div button {
            font-size: 18px;
        }

    section.projects > div .slogan a {
        width: 100%;
        margin-top: 20px;
        background-color: #142333;
        padding: 30px 10px;
    }

    section.projects > div .slogan > p {
        width: 100%;
    }

    section.gallery .grid .gallery {
        height: 500px;
    }

        section.gallery .grid .gallery a h3 {
            display: none;
        }
        section.gallery .grid .gallery a h2 {
            display: none;
        }
        section.gallery .grid .gallery a:nth-child(1) {
            height: 66.66%;
        }

        section.gallery .grid .gallery a:nth-child(4), section.gallery .grid .gallery a:nth-child(5) {
            top: 66.66%;
            height: 33.33%;
        }

        section.gallery .grid .gallery a:nth-child(6), section.gallery .grid .gallery a:nth-child(7) {
            display: none;
        }

    section.subpage-head {
        padding: 150px 0 50px;
    }

    section.vision > div h4 {
        margin-top: 20px;
    }

        section.vision > div h4 i {
            display: block;
            -webkit-transform: translateY(10px);
            transform: translateY(10px);
        }

    section.founder .grid {
        padding: 20px 200px 20px 20px;
    }

    section.docs > div > div > div {
        margin: 0 -0.5%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.docs > div > div > div > * {
            width: 49%;
            margin: 0 0.5% 15px;
        }

    section.subpage > div .list > div {
        margin: 0 -0.5%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.subpage > div .list > div > * {
            width: 49%;
            margin: 0 0.5% 10px;
        }

    section.projects-page > div .list > div {
        margin: 0 -1%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.projects-page > div .list > div > * {
            width: 98%;
            margin: 0 1% 15px;
        }

    section.project-detail > div .title {
        padding: 0;
        text-align: center;
    }

        section.project-detail > div .title a {
            position: relative;
            display: block;
        }

    section.project-detail > div .count > div > div {
        margin: 0 -1%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.project-detail > div .count > div > div > * {
            width: 48%;
            margin: 0 1% 15px;
        }

    section.project-detail > div .before-after > div {
        height: 300px;
    }

    section.project-detail > div .photos > a img {
        height: 400px;
    }
}

@media (max-width: 500px) {
    section.home-down .references > div .references-slider .slick-dots {
        display: none !important
    }

    .slogantext {
        font-size: 30px !important;
    }

    .tow > div {
        margin: 0 -1%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        .tow > div > * {
            width: 98%;
            margin: 0 1% 20px;
        }

    .loading svg {
        width: 300px;
    }

    h5 {
        font-size: 28px;
    }

        h5:before {
            height: 2px;
            width: 100px;
        }

    header > div > ul.offer {
        display: none;
    }

    section.slider a div h2 {
        font-size: 35px;
        margin-bottom: 40px;
        line-height: 1.5;
    }

    section.slider a div button {
        font-size: 19px;
        letter-spacing: 1px;
    }

    section.slider .slick-dots {
        margin: -50px auto 50px;
    }

        section.slider .slick-dots li button {
            padding: 5px;
        }

    section.projects > div .slogan a {
        margin-top: 0;
    }

    section.projects > div .projects p {
        font-size: 17px;
    }

    section.gallery .grid .gallery {
        height: 400px;
    }

    section.awards .grid > div > div:nth-child(1) img {
        width: 80%;
        margin: 0 10%;
    }

    section.founder .grid {
        padding: 20px;
    }

        section.founder .grid:before {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background-color: rgba(255, 255, 255, .65)
        }

        section.founder .grid > div {
            position: relative;
        }

    section.docs > div > div > div {
        margin: 0 -0.5%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.docs > div > div > div > * {
            width: 99%;
            margin: 0 0.5% 15px;
        }

    section.subpage > div h4 {
        font-size: 25px;
    }

        section.subpage > div h4 + p {
            font-size: 14px;
            padding: 0;
        }

    section.subpage > div ul li {
        width: 100%;
    }

    section.subpage > div .list > div {
        margin: 0 -0.5%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.subpage > div .list > div > * {
            width: 49%;
            margin: 0 0.5% 10px;
        }

    section.gallery-page > div .list a img {
        height: 150px;
    }

    section.gallery-page > div .list a h3 {
        display: none;
    }
    section.gallery-page > div .list a h2 {
        display: none;
    }
    section.gallery-page > div .list a div {
        width: 80px;
        height: 80px;
    }

        section.gallery-page > div .list a div div {
            width: 50px;
            height: 50px;
        }

            section.gallery-page > div .list a div div i {
                font-size: 18px;
            }

    section.projects-page > div .list > div {
        margin: 0 -1%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.projects-page > div .list > div > * {
            width: 98%;
            margin: 0 1% 15px;
        }

    section.project-detail > div .before-after > div {
        height: 200px;
    }

    section.project-detail > div .photos a i {
        right: 15px;
        bottom: 15px;
        font-size: 20px;
    }

    section.project-detail > div .photos > a img {
        height: 250px;
    }

    section.project-detail > div .photos > div > div {
        margin: 0 -1%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        section.project-detail > div .photos > div > div > * {
            width: 48%;
            margin: 0 1% 10px;
        }

    footer .footer > div {
        margin: 0 -1%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

        footer .footer > div > * {
            width: 98%;
            margin: 0 1% 40px;
        }
}

@media (max-width: 400px) {
    .slogantext {
        font-size: 25px !important;
    }

    header {
        padding: 15px 0;
    }

        header > div > a img {
            width: 170px;
        }

        header > div > ul.lang {
            padding: 0;
        }

        header > div > button {
            padding: 5px 0;
        }
}

div#comparison {
    width: 100%;
    height: 100%;
    max-height: 600px;
    overflow: hidden;
}

    div#comparison figure {
        background-image: url(../../../s3-us-west-2.amazonaws.com/s.cdpn.io/4273/photoshop-face-before.webp);
        background-size: cover;
        position: relative;
        font-size: 0;
        width: 100%;
        height: 100%;
        margin: 0;
    }

    div#comparison * {
        background-position: left;
    }

    div#comparison figure > img {
        position: relative;
        width: 100%;
    }

    div#comparison figure div {
        background-image: url(../../../s3-us-west-2.amazonaws.com/s.cdpn.io/4273/photoshop-face-after.webp);
        background-size: cover;
        position: absolute;
        width: 50%;
        -webkit-box-shadow: 0 5px 10px -2px rgba(0, 0, 0, .3);
        box-shadow: 0 5px 10px -2px rgba(0, 0, 0, .3);
        overflow: hidden;
        bottom: 0;
        height: 100%;
        border-right: 3px solid #fff;
        opacity: .8;
    }

input[type=range] {
    -webkit-appearance: none;
    -moz-appearance: none;
    position: relative;
    top: -50%;
    width: 100%;
}

    input[type=range]:focus {
        outline: none;
    }

    input[type=range]:active {
        outline: none;
    }

    input[type=range]::-moz-range-track {
        -moz-appearance: none;
        height: 15px;
        width: 98%;
        background-color: rgba(255, 255, 255, .1);
        position: relative;
        outline: none;
    }

    input[type=range]::active {
        border: none;
        outline: none;
    }

    input[type=range]::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: 1px solid #fff;
        background-image: url(../img/before-icon.png);
        background-size: 27px;
        background-position: center;
        background-repeat: no-repeat;
    }

    input[type=range]::-moz-range-thumb {
        -moz-appearance: none;
        width: 20px;
        height: 15px;
        background-color: #fff;
        border-radius: 0;
    }

    input[type=range]:focus::-webkit-slider-thumb {
        background-color: rgba(255, 255, 255, .5);
    }

    input[type=range]:focus::-moz-range-thumb {
        background-color: rgba(255, 255, 255, .5);
    }

[dir="rtl"] header > div.top a {
    margin: 0 0 0 10px;
    dir: ltr
}

[dir="rtl"] header > div.top .social {
    text-align: left
}

    [dir="rtl"] header > div.top .social a {
        margin: 0 10px 0 0
    }

[dir="rtl"] header > div.bottom > a, [dir="rtl"] section.projects > div .slogan > p {
    float: right;
}

[dir="rtl"] header > div.bottom > ul, [dir="rtl"] section.projects > div .slogan a {
    float: left;
}

[dir="rtl"] *, *:before, *:after {
    line-height: 1.2
}

[dir="rtl"] section.slider a div button:before, [dir="rtl"] h5:before, [dir="rtl"] section.projects > div .projects ul li:before, [dir="rtl"] section.projects > div .projects ul:before, [dir="rtl"] footer .footer h6:before {
    right: 0
}

[dir="rtl"] header > div.top > div > * {
    direction: ltr;
    text-align: right
}

[dir="rtl"] section.slider a div {
    padding: 30vh 0 0 100px
}

[dir="rtl"] section.projects > div .projects ul li i:nth-of-type(2) {
    transform: translateY(2px) rotate(180deg)
}

[dir="rtl"] footer .footer ul li a.all i, [dir="rtl"] footer .footer address.all i {
    transform: translateY(-1px) rotate(180deg)
}

[dir="rtl"] footer .footer ul.social i, [dir="rtl"] footer .footer ul.social svg {
    margin-right: 0;
    margin-left: 7px
}

[dir="rtl"] section.gallery .grid h5:before {
    right: auto;
}

[dir="rtl"] section.about .grid > div .slogantext {
    padding: 70px 0 0 100px
}

[dir="rtl"] section.home-down .references > div > a i {
    margin-left: 0;
    margin-right: 7px;
    transform: translateY(0) rotate(180deg)
}

[dir="rtl"] section.home-down .references > div > p {
    float: right
}

[dir="rtl"] section.home-down .references > div > a {
    float: left
}

[dir="rtl"] section.whyus .grid h5:before {
    right: 50%;
    transform: translateX(50%)
}

[dir="rtl"] section.founder .grid {
    transform: rotateY(180deg)
}

    [dir="rtl"] section.founder .grid > div {
        transform: rotateY(-180deg)
    }

    [dir="rtl"] section.founder .grid a i {
        margin-right: 0;
        margin-left: 5px
    }

[dir="rtl"] section.humanr ul li div span {
    right: auto;
    left: 0
}

[dir="rtl"] section.humanr ul li * {
    text-align: right
}

[dir="rtl"] section.project-detail > div .title {
    padding: 0 0 0 150px
}

    [dir="rtl"] section.project-detail > div .title a {
        right: auto;
        left: 0
    }

[dir="rtl"] form label {
    text-align: right
}

[dir="rtl"] section.gallery .grid .gallery a h3 {
    right: 20px;
    left: 40px;
    text-align: right
}

    [dir="rtl"] section.gallery .grid .gallery a h3:before, [dir="rtl"] section.subpage > div .list a h3:before {
        left: auto;
        right: 0
    }

[dir="rtl"] section.projects-page > div .list a h3, [dir="rtl"] section.projects-page > div .list a span, [dir="rtl"] section.gallery-page > div .list a h3 {
    text-align: right
}
[dir="rtl"] section.gallery .grid .gallery a h2 {
    right: 20px;
    left: 40px;
    text-align: right
}

    [dir="rtl"] section.gallery .grid .gallery a h2:before, [dir="rtl"] section.subpage > div .list a h2:before {
        left: auto;
        right: 0
    }

[dir="rtl"] section.projects-page > div .list a h2, [dir="rtl"] section.projects-page > div .list a span, [dir="rtl"] section.gallery-page > div .list a h2 {
    text-align: right
}
[dir="rtl"] h5 {
    padding-bottom: 20px
}

[dir="rtl"] section.projects-page #map .haritaacilan {
    text-align: right;
}

    [dir="rtl"] section.projects-page #map .haritaacilan div {
        float: right;
        margin: 0 0 0 10px
    }

    [dir="rtl"] section.projects-page #map .haritaacilan a i {
        transform: rotate(180deg);
        margin: 0 10px 0 0
    }

section.subpage-head ul li a i {
    transform: rotateY(180deg)
}

section.subpage.map-page {
    padding: 0
}

[dir="rtl"] header > div.bottom button {
    float: left
}

[lang="tr"] section.whyus .grid > div a span, [lang="en"] section.whyus .grid > div a span {
    height: 250px
}

@media (max-width: 500px) {
    section.whyus .grid > div a span {
        height: auto !important
    }

    section.projects > div .projects ul li {
        padding: 15px 0 15px 15px;
        font-size: 14px
    }
}
