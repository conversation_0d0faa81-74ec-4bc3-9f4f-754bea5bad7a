<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="360_gallery" xml:space="preserve">
    <value>360 degree gallery</value>
  </data>
  <data name="about-nasa" xml:space="preserve">
    <value>Always striving</value>
  </data>
  <data name="about-nasa2" xml:space="preserve">
    <value>for the best</value>
  </data>
  <data name="about_slogan" xml:space="preserve">
    <value>We draw your dreams together with designs &lt;span&gt; Between creativity and modernity&lt;/span&gt;</value>
  </data>
  <data name="address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="address_title" xml:space="preserve">
    <value>Adress</value>
  </data>
  <data name="all_projects" xml:space="preserve">
    <value>All Projects</value>
  </data>
  <data name="all_references" xml:space="preserve">
    <value>All Clients</value>
  </data>
  <data name="apply_now" xml:space="preserve">
    <value>Apply Now</value>
  </data>
  <data name="awards_received" xml:space="preserve">
    <value>Awards We Won</value>
  </data>
  <data name="before_after" xml:space="preserve">
    <value>Before after</value>
  </data>
  <data name="blog" xml:space="preserve">
    <value>Blog</value>
  </data>
  <data name="career_text" xml:space="preserve">
    <value>SMD Group believes that successful employees are the key to company success. Therefore, we place great importance on attracting outstanding talent. At SMD Group, we always focus on developing our workforce and taking our company to the highest level of performance.

We are constantly looking for promising talents who are willing to prove their worth and work towards our professional goals and their own aspirations. We strive to enrich our team with experienced professionals who bring fresh and valuable contributions to our work.

If you have experience in our field of work, a young age, high ambition, a spirit of fair competition, and a drive to achieve our professional goals, you can be part of the SMD Group team by filling out the job application form and selecting the position that suits your experience and skills. Please review the application requirements for our vacant positions.

Our vision for work and employment focuses on teamwork, respecting employee rights, and applying humane and ethical standards in our dealings</value>
  </data>
  <data name="career_title" xml:space="preserve">
    <value>&lt;h4&gt;Please fill in the &lt;span&gt;field&lt;/span&gt; to apply for a job&lt;/h4&gt;</value>
  </data>
  <data name="click_to_view" xml:space="preserve">
    <value>Click to View Documents as PDF</value>
  </data>
  <data name="company_name" xml:space="preserve">
    <value>SMD DECORATION</value>
  </data>
  <data name="contact_form_text" xml:space="preserve">
    <value>SMD GROUP's team is with you around the clock. We are with you from design to turnkey delivery. We take pride in being one of the best decoration companies in Turkey. Our team is specialized and professional in the field of interior design and decoration. We provide our services for designing and executing interior decoration projects for homes, apartments, villas, offices, commercial stores, and other large projects. We care about the project details and strive to achieve the client's vision in the best possible way. Based on your needs and personal taste, we offer innovative and contemporary designs that reflect your unique style and meet your functional requirements. Whether you are looking for a distinctive kitchen design or a comprehensive interior design for your home, we can make it happen for you. Feel free to contact us via WhatsApp or fill out the form below. We will be happy to assist you in turning your ideas into tangible reality.</value>
  </data>
  <data name="contact_form_title" xml:space="preserve">
    <value />
  </data>
  <data name="contact_text" xml:space="preserve">
    <value>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</value>
  </data>
  <data name="contact_title" xml:space="preserve">
    <value>You can find some examples at</value>
  </data>
  <data name="contactus" xml:space="preserve">
    <value>Contact Us</value>
  </data>
  <data name="date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="date_of_birth" xml:space="preserve">
    <value>Date of birth</value>
  </data>
  <data name="email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="female" xml:space="preserve">
    <value>Female</value>
  </data>
  <data name="founder_name" xml:space="preserve">
    <value>Name Surname</value>
  </data>
  <data name="g360_gallery_title" xml:space="preserve">
    <value>360 degree gallery</value>
  </data>
  <data name="gender" xml:space="preserve">
    <value>Select your gender</value>
  </data>
  <data name="get_offer" xml:space="preserve">
    <value>Get Offer</value>
  </data>
  <data name="get_offer_btn" xml:space="preserve">
    <value>Get Offer</value>
  </data>
  <data name="get_offer_text" xml:space="preserve">
    <value>Take your step and communicate with specialists in the world of engineering to give you the best competitive prices</value>
  </data>
  <data name="go_back" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="home_our_projects" xml:space="preserve">
    <value>Our Projects</value>
  </data>
  <data name="home_ref_title" xml:space="preserve">
    <value />
  </data>
  <data name="home_slogan" xml:space="preserve">
    <value>We are a company specialized in luxury residential and commercial projects, decoration, interior design and exterior cladding. We have done amazing projects in many Turkish provinces to be on top of the decoration companies in Turkey. Our designs are professional and passionate about meeting our clients' ambitions.
                         &lt;br&gt;&lt;br&gt;
                         We use technical and technical cadres with high capabilities, which in turn guarantee you accuracy in design and quality in implementation and supervision</value>
  </data>
  <data name="how_can_we_get" xml:space="preserve">
    <value>How Can We Get Back?</value>
  </data>
  <data name="how_email" xml:space="preserve">
    <value>How to Call Back?</value>
  </data>
  <data name="how_phone" xml:space="preserve">
    <value>Phone Call</value>
  </data>
  <data name="how_sms" xml:space="preserve">
    <value>Sms</value>
  </data>
  <data name="istanbul_vd" xml:space="preserve">
    <value>İstanbul.VD.</value>
  </data>
  <data name="istanbul_vd_number" xml:space="preserve">
    <value>00330044</value>
  </data>
  <data name="job" xml:space="preserve">
    <value>Choose your profession</value>
  </data>
  <data name="logo_alt" xml:space="preserve">
    <value>SMD | SMD Decoration Architecture</value>
  </data>
  <data name="mail_and_fax" xml:space="preserve">
    <value>Mail And Fax</value>
  </data>
  <data name="male" xml:space="preserve">
    <value>Male</value>
  </data>
  <data name="message" xml:space="preserve">
    <value>Massage</value>
  </data>
  <data name="messages" xml:space="preserve">
    <value>Massages</value>
  </data>
  <data name="name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="other_info" xml:space="preserve">
    <value>Other Informations</value>
  </data>
  <data name="our_documents" xml:space="preserve">
    <value>Our Documents</value>
  </data>
  <data name="our_founder" xml:space="preserve">
    <value>Our Founder</value>
  </data>
  <data name="our_founder_desc" xml:space="preserve">
    <value>SMD Group was established, the first and leading company in the field of interior design in Turkey, smd decoration, managed by Eng. Ahmed Marwan Anis, and he provided the best services to his customers in every step he made after graduating from Aleppo University in Syria in 2012 AD, and we as a smd decoration family believe that our success And his success has benefited all our customers</value>
  </data>
  <data name="our_founder_text" xml:space="preserve">
    <value>M. Marwan Anis</value>
  </data>
  <data name="our_projects" xml:space="preserve">
    <value>Our Projects</value>
  </data>
  <data name="our_projects_text" xml:space="preserve">
    <value>You can find some examples of our &lt;span&gt; work by viewing our gallery. &lt;/span&gt;</value>
  </data>
  <data name="our_services" xml:space="preserve">
    <value>Our services</value>
  </data>
  <data name="our_services_text" xml:space="preserve">
    <value>2D and 3D design, shop drawings and quantity calculations
Supervision and implementation of all types of internal and external projects
Manufacture of all kinds of engineering models</value>
  </data>
  <data name="photo_gallery" xml:space="preserve">
    <value>SMD Gallery</value>
  </data>
  <data name="photo_gallery_title" xml:space="preserve">
    <value />
  </data>
  <data name="project_map_text" xml:space="preserve">
    <value>SMD GROUP operates from Turkey and to all countries of the world and in all Turkish regions SMD GROUP is everywhere</value>
  </data>
  <data name="project_map_title" xml:space="preserve">
    <value>You can view some examples of our work</value>
  </data>
  <data name="project_see_more" xml:space="preserve">
    <value>Click to View Project Details</value>
  </data>
  <data name="project_video" xml:space="preserve">
    <value>Project Video</value>
  </data>
  <data name="projects_as_countries" xml:space="preserve">
    <value>Projects And Countries</value>
  </data>
  <data name="projects_countries" xml:space="preserve">
    <value>Projects And Countries</value>
  </data>
  <data name="projects_countries_text" xml:space="preserve">
    <value>SMD GROUP is not only in Turkey but all over the world</value>
  </data>
  <data name="see_all" xml:space="preserve">
    <value>See All</value>
  </data>
  <data name="see_countries" xml:space="preserve">
    <value>Watch Our projects</value>
  </data>
  <data name="see_projects" xml:space="preserve">
    <value>Look around the projects</value>
  </data>
  <data name="social_media" xml:space="preserve">
    <value>Social Media</value>
  </data>
  <data name="starting_date" xml:space="preserve">
    <value>Starting date</value>
  </data>
  <data name="submit_the_form" xml:space="preserve">
    <value>Submit Form</value>
  </data>
  <data name="surname" xml:space="preserve">
    <value>Surname</value>
  </data>
  <data name="tel" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="telephone" xml:space="preserve">
    <value>Telephone</value>
  </data>
  <data name="video_gallery" xml:space="preserve">
    <value>Video Gallery</value>
  </data>
  <data name="video_gallery_title" xml:space="preserve">
    <value>Video Gallery</value>
  </data>
  <data name="view_all" xml:space="preserve">
    <value>View All</value>
  </data>
  <data name="view_awards" xml:space="preserve">
    <value>View Awards</value>
  </data>
  <data name="view_details" xml:space="preserve">
    <value>View Details</value>
  </data>
  <data name="view_document" xml:space="preserve">
    <value>View Documents</value>
  </data>
  <data name="vision_mission" xml:space="preserve">
    <value>Vision And Mision</value>
  </data>
  <data name="vision_mission_text" xml:space="preserve">
    <value>Our vision &lt;br&gt; In your home, office, workplace, or investment, we aim to create beauty and sophistication wherever you are. It starts from the idea of a real estate project, passing through the stages of its implementation, until marketing and selling the project, in addition to interior design and decoration. For us, it is not just a service we provide, but rather a lifestyle we create. Designs that put you in the heart of modernity. Practical or aesthetic Excellence in design and its success in implementation, quality and research are fundamental to our approach. Each project is a unique opportunity to value the client’s vision and exceed his expectations &lt;br&gt; Our goal &lt;br&gt; Our goal and the goal of our customers are one and we all work with one hand to achieve all of us the desired dream within the limits Allocated budgets, high quality and resounding success.</value>
  </data>
  <data name="we_always_wordk" xml:space="preserve">
    <value>&lt;span&gt;We strive&lt;/span&gt; &lt;i class="fi flaticon-quality-1"&gt;&lt;/i&gt; always for the best</value>
  </data>
  <data name="whyus" xml:space="preserve">
    <value>Why us</value>
  </data>
  <data name="whyus_text" xml:space="preserve">
    <value>&lt;p style="text-align:right;"&gt;We represent Turkish and European companies and factories working in the fields of:&lt;/p&gt;&lt;p style="text-align:right;"&gt;&amp;nbsp;&lt;/p&gt;&lt;p style="text-align:right;"&gt;- Hotel and restaurant equipment and supplies&lt;/p&gt;&lt;p style="text-align: right;"&gt;- All types of natural and artificial stone, marble, ceramics and parquet&lt;/p&gt;&lt;p style="text-align:right;"&gt;- Securing all types of external and internal fire-fighting doors and windows, etc.&lt;/p&gt;&lt;p style="text-align:right;"&gt;- Sanitary, bathroom and kitchen equipment and accessories&lt;/p&gt;&lt;p p&gt;&lt;p style="text-align:right;"&gt;- Office, home and kitchen furnishings of all kinds&lt;/p&gt;&lt;p style="text-align:right;"&gt;- Electrical accessories&lt;/p&gt;&lt;p style=" text-align:right;"&gt;- Electric mobile ceiling fixtures&lt;/p&gt;</value>
  </data>
  <data name="year_winner" xml:space="preserve">
    <value>Year Winner:</value>
  </data>
  <data name="about_us" xml:space="preserve">
    <value>About Us</value>
  </data>

  <data name="gallary" xml:space="preserve">
    <value>gallery</value>
  </data>
  <data name="ourblog" xml:space="preserve">
    <value>Our news</value>
  </data>
  <data name="our_work" xml:space="preserve">
    <value>Our Clients</value>
  </data>
  <data name="whysmd" xml:space="preserve">
    <value>Why SMD group</value>
  </data>
  <data name="coproject" xml:space="preserve">
    <value>Commercial projects</value>
  </data>
  <data name="hr" xml:space="preserve">
    <value>Human Resources</value>
  </data>
  <data name="project" xml:space="preserve">
    <value>projects</value>
  </data>
  <data name="resproject" xml:space="preserve">
    <value>Residential Projects</value>
  </data>
  <data name="video" xml:space="preserve">
    <value>Video</value>
  </data>
  <data name="from" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="proj23" xml:space="preserve">
    <value>Page</value>
    <comment>مشاريعنا</comment>
  </data>
  <data name="proj24" xml:space="preserve">
    <value>From</value>
    <comment>مشاريعنا</comment>
  </data>
  <data name="qslider" xml:space="preserve">
    <value>For inquiries and contact:</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value>360 Degree </value>
  </data>
  <data name="String265" xml:space="preserve">
    <value>Return to the list</value>
  </data>
  <data name="String302" xml:space="preserve">
    <value>are sure of the deleting process?</value>
  </data>
  <data name="String308" xml:space="preserve">
    <value>Delete the item</value>
  </data>
  <data name="String317" xml:space="preserve">
    <value>item details</value>
  </data>
  <data name="String330" xml:space="preserve">
    <value>Created At</value>
  </data>
  <data name="String331" xml:space="preserve">
    <value>Modified date</value>
  </data>
  <data name="String10" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="String11" xml:space="preserve">
    <value>delete</value>
  </data>
  <data name="String12" xml:space="preserve">
    <value>Add an item</value>
  </data>
  <data name="String13" xml:space="preserve">
    <value>Modify an item</value>
  </data>
  <data name="String2" xml:space="preserve">
    <value>add</value>
  </data>
  <data name="String3" xml:space="preserve">
    <value>Management of awards</value>
  </data>
  <data name="String4" xml:space="preserve">
    <value>address</value>
  </data>
  <data name="String5" xml:space="preserve">
    <value>Year</value>
  </data>
  <data name="String6" xml:space="preserve">
    <value>Show on the first page</value>
  </data>
  <data name="String7" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="String8" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="String9" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="String14" xml:space="preserve">
    <value>Lang:</value>
  </data>
  <data name="String15" xml:space="preserve">
    <value>Choose</value>
  </data>
  <data name="String16" xml:space="preserve">
    <value>Link</value>
  </data>
  <data name="String17" xml:space="preserve">
    <value>Arabic Lang</value>
  </data>
  <data name="String18" xml:space="preserve">
    <value>Content</value>
  </data>
  <data name="String19" xml:space="preserve">
    <value>Upload Images</value>
  </data>
  <data name="String20" xml:space="preserve">
    <value>Upload File</value>
  </data>
  <data name="String21" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="String22" xml:space="preserve">
    <value>Block Management</value>
  </data>
  <data name="String23" xml:space="preserve">
    <value>Edit Block </value>
  </data>
  <data name="String31" xml:space="preserve">
    <value>Add Award</value>
  </data>
  <data name="String32" xml:space="preserve">
    <value>Modify Award</value>
  </data>
  <data name="thanks" xml:space="preserve">
    <value>Thanks</value>
    <comment>شكرا</comment>
  </data>
  <data name="thanks1" xml:space="preserve">
    <value>Your request has been received and you will be contacted to follow up</value>
    <comment>شكرا</comment>
  </data>
  <data name="String24" xml:space="preserve">
    <value>The field is required</value>
  </data>
  <data name="String25" xml:space="preserve">
    <value>Please type the email correctly</value>
  </data>
  <data name="String26" xml:space="preserve">
    <value>Please enter a valid phone number</value>
  </data>
  <data name="String27" xml:space="preserve">
    <value>Choose your profession</value>
  </data>
  <data name="String28" xml:space="preserve">
    <value>Choose your profession</value>
  </data>
  <data name="String29" xml:space="preserve">
    <value>Video Editor</value>
  </data>
  <data name="String30" xml:space="preserve">
    <value>accountant</value>
  </data>
  <data name="String33" xml:space="preserve">
    <value>Content Writer</value>
  </data>
  <data name="String34" xml:space="preserve">
    <value>designer</value>
  </data>
  <data name="String35" xml:space="preserve">
    <value>programmer</value>
  </data>
  <data name="String36" xml:space="preserve">
    <value>Graphic Designer</value>
  </data>
  <data name="String37" xml:space="preserve">
    <value>Architect</value>
  </data>
  <data name="String38" xml:space="preserve">
    <value>coordinator</value>
  </data>
  <data name="String39" xml:space="preserve">
    <value>Project Manager</value>
  </data>
  <data name="String40" xml:space="preserve">
    <value>address</value>
  </data>
  <data name="String41" xml:space="preserve">
    <value>Other information</value>
  </data>
  <data name="String42" xml:space="preserve">
    <value>Slider</value>
  </data>
  <data name="String43" xml:space="preserve">
    <value>most visited news</value>
  </data>
  <data name="String44" xml:space="preserve">
    <value>most visited projects</value>
  </data>
  <data name="String45" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="String46" xml:space="preserve">
    <value>Website</value>
  </data>
  <data name="String47" xml:space="preserve">
    <value>Project Category</value>
  </data>
  <data name="String48" xml:space="preserve">
    <value>Post Category</value>
  </data>
  <data name="String49" xml:space="preserve">
    <value>SEO Page</value>
  </data>
  <data name="String50" xml:space="preserve">
    <value>Add post</value>
  </data>
  <data name="String51" xml:space="preserve">
    <value>Modify post</value>
  </data>
  <data name="String52" xml:space="preserve">
    <value>add project</value>
  </data>
  <data name="String53" xml:space="preserve">
    <value>Modify project</value>
  </data>
  <data name="String54" xml:space="preserve">
    <value>Various</value>
  </data>
  <data name="String55" xml:space="preserve">
    <value>Add 360 Degree</value>
  </data>
  <data name="String56" xml:space="preserve">
    <value>Modify 360 Degree</value>
  </data>
  <data name="String57" xml:space="preserve">
    <value>Add video</value>
  </data>
  <data name="String58" xml:space="preserve">
    <value>Modify video</value>
  </data>
  <data name="String59" xml:space="preserve">
    <value>Add Stuff</value>
  </data>
  <data name="String60" xml:space="preserve">
    <value>Modify Stuff</value>
  </data>
  <data name="String61" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="String62" xml:space="preserve">
    <value>Passwoed</value>
  </data>
  <data name="String63" xml:space="preserve">
    <value>Permission</value>
  </data>
  <data name="String64" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="String65" xml:space="preserve">
    <value>360 Degree</value>
  </data>
  <data name="String66" xml:space="preserve">
    <value>cover image</value>
  </data>
  <data name="String67" xml:space="preserve">
    <value>smd videos</value>
  </data>
  <data name="String68" xml:space="preserve">
    <value>smd videos categories</value>
  </data>
  <data name="String69" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="String70" xml:space="preserve">
    <value>Page Management</value>
  </data>
  <data name="String71" xml:space="preserve">
    <value>Edit Page</value>
  </data>
  <data name="String72" xml:space="preserve">
    <value>Num Of Visit</value>
  </data>
  <data name="String73" xml:space="preserve">
    <value>Posts Categories Management</value>
  </data>
  <data name="String74" xml:space="preserve">
    <value>Create Post Category</value>
  </data>
  <data name="String75" xml:space="preserve">
    <value>Edit Post Category</value>
  </data>
  <data name="String76" xml:space="preserve">
    <value>Details Post Category</value>
  </data>
  <data name="String77" xml:space="preserve">
    <value>Delete Post Category</value>
  </data>
  <data name="String78" xml:space="preserve">
    <value>ProjectsCategories Management</value>
  </data>
  <data name="String79" xml:space="preserve">
    <value>Create Project Category</value>
  </data>
  <data name="String80" xml:space="preserve">
    <value>Edit Project Category</value>
  </data>
  <data name="String81" xml:space="preserve">
    <value>Details Project Category</value>
  </data>
  <data name="String82" xml:space="preserve">
    <value>Delete Project Category</value>
  </data>
  <data name="String83" xml:space="preserve">
    <value>Arrange</value>
  </data>
  <data name="String84" xml:space="preserve">
    <value>360 Degress Management</value>
  </data>
  <data name="String85" xml:space="preserve">
    <value>Videos Management</value>
  </data>
  <data name="String86" xml:space="preserve">
    <value>Post Category</value>
  </data>
  <data name="String87" xml:space="preserve">
    <value>Project Category</value>
  </data>
  <data name="String88" xml:space="preserve">
    <value>Publish</value>
  </data>
  <data name="String89" xml:space="preserve">
    <value>Show Whats app</value>
  </data>
  <data name="String90" xml:space="preserve">
    <value>Show Comments</value>
  </data>
  <data name="String91" xml:space="preserve">
    <value>Auther</value>
  </data>
  <data name="String92" xml:space="preserve">
    <value>Latitude</value>
  </data>
  <data name="String93" xml:space="preserve">
    <value>Longitude</value>
  </data>
  <data name="String94" xml:space="preserve">
    <value>Project Gallary</value>
  </data>
  <data name="String95" xml:space="preserve">
    <value>Create Project Gallary</value>
  </data>
  <data name="String96" xml:space="preserve">
    <value>Post</value>
  </data>
  <data name="String100" xml:space="preserve">
    <value>details of client</value>
  </data>
  <data name="String101" xml:space="preserve">
    <value>delete client</value>
  </data>
  <data name="String102" xml:space="preserve">
    <value>Slider Management</value>
  </data>
  <data name="String103" xml:space="preserve">
    <value>add slider</value>
  </data>
  <data name="String104" xml:space="preserve">
    <value>edit slider</value>
  </data>
  <data name="String105" xml:space="preserve">
    <value>details of slider</value>
  </data>
  <data name="String106" xml:space="preserve">
    <value>delete slider</value>
  </data>
  <data name="String107" xml:space="preserve">
    <value>Video Categories Management</value>
  </data>
  <data name="String108" xml:space="preserve">
    <value>Add Video Category</value>
  </data>
  <data name="String109" xml:space="preserve">
    <value>edit video category</value>
  </data>
  <data name="String110" xml:space="preserve">
    <value>details of video category</value>
  </data>
  <data name="String111" xml:space="preserve">
    <value>delete video category</value>
  </data>
  <data name="String112" xml:space="preserve">
    <value>Smd videos management</value>
  </data>
  <data name="String113" xml:space="preserve">
    <value>add Smd videos</value>
  </data>
  <data name="String114" xml:space="preserve">
    <value>edit Smd videos</value>
  </data>
  <data name="String115" xml:space="preserve">
    <value>details of Smd videos</value>
  </data>
  <data name="String97" xml:space="preserve">
    <value>Client Management</value>
  </data>
  <data name="String98" xml:space="preserve">
    <value>Add client</value>
  </data>
  <data name="String99" xml:space="preserve">
    <value>edit client</value>
  </data>
  <data name="String116" xml:space="preserve">
    <value>delete Smd videos</value>
  </data>
  <data name="String117" xml:space="preserve">
    <value>FAQ</value>
  </data>
  <data name="String118" xml:space="preserve">
    <value>FAQ Management</value>
  </data>
  <data name="String119" xml:space="preserve">
    <value>Add FAQ</value>
  </data>
  <data name="String120" xml:space="preserve">
    <value>Edit FAQ</value>
  </data>
  <data name="String121" xml:space="preserve">
    <value>Details of FAQ</value>
  </data>
  <data name="String122" xml:space="preserve">
    <value>Delete FAQ</value>
  </data>
  <data name="String123" xml:space="preserve">
    <value>question</value>
  </data>
  <data name="String124" xml:space="preserve">
    <value>answer</value>
  </data>
  <data name="String125" xml:space="preserve">
    <value>Image Center</value>
  </data>
  <data name="String126" xml:space="preserve">
    <value>Add Group Of Photos</value>
  </data>
  <data name="String127" xml:space="preserve">
    <value>Copy Link</value>
  </data>
  <data name="category" xml:space="preserve">
    <value>The most important categories in decoration</value>
  </data>
  <data name="important" xml:space="preserve">
    <value>Important links</value>
  </data>
  <data name="jobs" xml:space="preserve">
    <value>Job opportunities </value>
  </data>
  <data name="nslogan" xml:space="preserve">
    <value>SMD Decoration is the most &lt;span  style=" color: #bf9d7f;"&gt;important decoration company in Istanbul&lt;/span&gt;, in addition to its work in Saudi Arabia, the Emirates, Qatar, Libya and Iraq</value>
  </data>
  <data name="cv" xml:space="preserve">
    <value>Your CV</value>
  </data>
  <data name="our_foun_text" xml:space="preserve">
    <value>The SMD Group was established as the first and leading company in the field of decoration design in Turkey, smd decoration, managed by Eng. And its success has benefited all of our customers</value>
  </data>
  <data name="file" xml:space="preserve">
    <value>Send File</value>
  </data>
  <data name="saudi" xml:space="preserve">
    <value>We are in Saudi Arabia</value>
  </data>
  <data name="suaditext" xml:space="preserve">
    <value>If you are looking for innovative ideas in decoration and interior design, you've come to the right place. We offer unique solutions that blend elegance and comfort to design spaces that meet your needs and suit your personal taste. Whether you're looking to renovate your home or design your office with the latest modern decor styles, we are here to provide you with the best consultations and services. Follow our new projects now in Saudi Arabia as we continue expanding our business and offering our expertise in our new branches. Don't miss the chance, log in now to stay updated!</value>
  </data>
  <data name="String128" xml:space="preserve">
    <value>Offers site</value>
  </data>
  <data name="String129" xml:space="preserve">
    <value>Our company's official Saudi website</value>
  </data>
</root>