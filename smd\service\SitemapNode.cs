﻿using smd.Models;
using System;
using System.Xml;

namespace smd.services
{
    public class SitemapNode
    {
        public SitemapFrequency? Frequency { get; set; }
        public DateTime? LastModified { get; set; }
        public double? Priority { get; set; }
        public string Url { get; set; }
    }

    public class SitemapIndexNode
    {
        public string Url { get; set; }
        public DateTime? LastModified { get; set; }
    }

    public enum SitemapFrequency
    {
        Never,
        Yearly,
        Monthly,
        Weekly,
        Daily,
        Hourly,
        Always
    }

}