using HtmlAgilityPack;
using Newtonsoft.Json.Linq;
using PagedList;
using smd.Filters;
using smd.Modals;
using smd.Models;
using smd.service;
using smd.services;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mail;
using System.ServiceModel.Syndication;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Web.Configuration;
using System.Web.Mvc;
using System.Xml;
using System.Xml.Linq;

namespace smd.Controllers
{
    public class HomeController : BaseController
    {
        private smddb db = new smddb();


        public ActionResult GenerateRSSpost(string id)
        {
            if (id == "posts")
            {
                // Create the XML document and root element for the RSS feed
                var doc = new XmlDocument();
                var rss = doc.CreateElement("rss");
                rss.SetAttribute("version", "2.0");
                doc.AppendChild(rss);

                // Create the "channel" element and add it to the RSS feed
                var channel = doc.CreateElement("channel");
                rss.AppendChild(channel);
                string lang = ViewBag.lang;
                smdservices t = new smdservices();

                // Add the required elements to the "channel" element
                var title = doc.CreateElement("title");
                title.InnerText = t.pagetitle(4, ViewBag.lang);
                channel.AppendChild(title);

                var link = doc.CreateElement("link");
                link.InnerText = "https://smddecoration.com/feed/posts";
                channel.AppendChild(link);

                var description = doc.CreateElement("description");
                description.InnerText = t.pageogdes(4, ViewBag.lang);
                channel.AppendChild(description);
                // Add the atom:link element
                var atomNamespace = "http://www.w3.org/2005/Atom";
                doc.DocumentElement.SetAttribute("xmlns:atom", atomNamespace);

                var atomLink = doc.CreateElement("atom:link", atomNamespace);
                atomLink.SetAttribute("href", "https://smddecoration.com/feed/posts");
                atomLink.SetAttribute("rel", "self");
                atomLink.SetAttribute("type", "application/rss+xml");
                channel.AppendChild(atomLink);
                var post = db.posts.Where(a => a.publish && a.lang == lang).OrderByDescending(a => a.arrange).ToList();

                // Add each item to the "channel" element
                foreach (var item in post)

                {
                    /*string urlenc = t.EncodeUrlIfNeeded(item.slogan);*/ string urlenc = item.slogan;


                    string valueart = "/" + t.getimagewithid(item.guid, "1");
                    valueart = t.EncodeSpaces(valueart);
                    var item1 = CreateRSSItem(doc, item.title, item.ogdescription, "https://smddecoration.com/post/" + urlenc, item.datemodified, "https://smddecoration.com" + valueart);
                    channel.AppendChild(item1);

                }
                // Set the content type and return the XML document as a content result
                Response.ContentType = "application/rss+xml";
                return this.Content(doc.InnerXml, "text/xml", Encoding.UTF8);
            }
            else {
                // Create the XML document and root element for the RSS feed
                var doc = new XmlDocument();
                var rss = doc.CreateElement("rss");
                rss.SetAttribute("version", "2.0");
                doc.AppendChild(rss);

                // Create the "channel" element and add it to the RSS feed
                var channel = doc.CreateElement("channel");
                rss.AppendChild(channel);
                string lang = ViewBag.lang;
                smdservices t = new smdservices();

                // Add the required elements to the "channel" element
                var title = doc.CreateElement("title");
                title.InnerText = t.pagetitle(5, ViewBag.lang);
                channel.AppendChild(title);

                var link = doc.CreateElement("link");
                link.InnerText = "https://smddecoration.com/feed/projects";
                channel.AppendChild(link);

                var description = doc.CreateElement("description");
                description.InnerText = t.pageogdes(5, ViewBag.lang);
                channel.AppendChild(description);
                // Add the atom:link element
                var atomNamespace = "http://www.w3.org/2005/Atom";
                doc.DocumentElement.SetAttribute("xmlns:atom", atomNamespace);

                var atomLink = doc.CreateElement("atom:link", atomNamespace);
                atomLink.SetAttribute("href", "https://smddecoration.com/feed/projects");
                atomLink.SetAttribute("rel", "self");
                atomLink.SetAttribute("type", "application/rss+xml");
                channel.AppendChild(atomLink);
                var post = db.projects.Where(a => a.publish && a.lang == lang).ToList();

                // Add each item to the "channel" element
                foreach (var item in post)

                {
                    /*string urlenc = t.EncodeUrlIfNeeded(item.slogan);*/
                    string urlenc = item.slogan;

                    string valueart = "/" + t.getimagewithtype(item.guid, "project");
                    valueart = t.EncodeSpaces(valueart);
                    var item1 = CreateRSSItem(doc, item.title, item.ogdescription, "https://smddecoration.com/project/" + urlenc, item.datemodified, "https://smddecoration.com" + valueart);
                    channel.AppendChild(item1);

                }
                // Set the content type and return the XML document as a content result
                Response.ContentType = "application/rss+xml";
                return this.Content(doc.InnerXml, "text/xml", Encoding.UTF8);
            }
        }

        private XmlElement CreateRSSItem(XmlDocument doc, string title, string summary, string link, DateTime publishDate, string imageUrl)
        {
            // Create the "item" element
            var item = doc.CreateElement("item");

            // Add the required elements to the "item" element
            var itemTitle = doc.CreateElement("title");
            itemTitle.InnerText = title;
            item.AppendChild(itemTitle);

            var itemLink = doc.CreateElement("link");
            itemLink.InnerText = EncodeUrl(link);
            item.AppendChild(itemLink);

            var itemDescription = doc.CreateElement("description");
            itemDescription.InnerText = summary;
            item.AppendChild(itemDescription);

            var itemPubDate = doc.CreateElement("pubDate");
            itemPubDate.InnerText = publishDate.ToString("R");
            item.AppendChild(itemPubDate);

            // Add the image as an enclosure
            var itemImage = doc.CreateElement("enclosure");
            itemImage.SetAttribute("url", imageUrl);
            itemImage.SetAttribute("type", "image/webp"); // Change the type if needed
            itemImage.SetAttribute("length", GetFileSize(imageUrl).ToString());
            item.AppendChild(itemImage);

            // Add the guid element
            var itemGuid = doc.CreateElement("guid");
            itemGuid.InnerText = EncodeUrl(link);
            item.AppendChild(itemGuid);

            return item;
        }

        // This function encodes the URL to ensure it doesn't contain any non-ASCII characters
        private string EncodeUrl(string url)
        {
            return Uri.EscapeUriString(url);
        }

        // This function fetches the file size of a remote file
        private long GetFileSize(string url)
        {
            var webRequest = HttpWebRequest.Create(url);
            webRequest.Method = "HEAD";
            using (var webResponse = webRequest.GetResponse())
            {
                return long.Parse(webResponse.Headers.Get("Content-Length"));
            }
        }
        public ActionResult SitemapIndex()
        {
            var sitemapIndexNodes = new List<SitemapIndexNode>
            {
                new SitemapIndexNode
                {
                    Url = "https://smddecoration.com/sitemap.xml",
                    LastModified = DateTime.Now
                },
                new SitemapIndexNode
                {
                    Url = "https://smddecoration.com/en/sitemap.xml",
                    LastModified = DateTime.Now
                },
                new SitemapIndexNode
                {
                    Url = "https://smddecoration.com/tr/sitemap.xml",
                    LastModified = DateTime.Now
                }
            };

            string xml = GetSitemapIndexDocument(sitemapIndexNodes);
            return this.Content(xml, "text/xml", Encoding.UTF8);
        }

        private string GetSitemapIndexDocument(IEnumerable<SitemapIndexNode> sitemapIndexNodes)
        {
            XNamespace xmlns = "http://www.sitemaps.org/schemas/sitemap/0.9";
            XElement root = new XElement(xmlns + "sitemapindex");

            foreach (var node in sitemapIndexNodes)
            {
                XElement sitemapElement = new XElement(
                    xmlns + "sitemap",
                    new XElement(xmlns + "loc", Uri.EscapeUriString(node.Url)),
                    node.LastModified == null ? null : new XElement(
                        xmlns + "lastmod",
                        node.LastModified.Value.ToString("yyyy-MM-ddTHH:mm:sszzz")));
                root.Add(sitemapElement);
            }

            XDocument document = new XDocument(root);
            return document.ToString();
        }
        
        public ActionResult SitemapXml()
        {

            var sitemapNodes = GetSitemapNodes();
            string xml = GetSitemapDocument(sitemapNodes);

            return this.Content(xml, "text/xml", Encoding.UTF8);
        }
        public ActionResult SitemapXmltag()
        {

            var sitemapNodes = GetSitemapNodestag();
            string xml = GetSitemapDocument(sitemapNodes);

            return this.Content(xml, "text/xml", Encoding.UTF8);
        }

        public IReadOnlyCollection<SitemapNode> GetSitemapNodestag()
        {
            string lang = ViewBag.lang;
            List<SitemapNode> nodes = new List<SitemapNode>();


            foreach (var news in db.jobs.Where(a => a.lang == "ar").Select(a => a.title).ToList())
            {
                string tt = news.Replace(".", "");
                tt = news.Replace(":", "");
                tt= news.Replace("؟", "");
                nodes.Add(
                   new SitemapNode()
                   {
                       Url = "https://smddecoration.com/tag/" + tt,
                       Frequency = SitemapFrequency.Daily,
                       Priority = 0.8
                   });
            }



            return nodes;
        }
        public string GetSitemapDocument(IEnumerable<SitemapNode> sitemapNodes)
        {
            XNamespace xmlns = "http://www.sitemaps.org/schemas/sitemap/0.9";
            XElement root = new XElement(xmlns + "urlset");

            foreach (SitemapNode sitemapNode in sitemapNodes)
            {
                XElement urlElement = new XElement(
                    xmlns + "url",
                    new XElement(xmlns + "loc", Uri.EscapeUriString(sitemapNode.Url)),
                    sitemapNode.LastModified == null ? null : new XElement(
                        xmlns + "lastmod",
                        sitemapNode.LastModified.Value.ToLocalTime().ToString("yyyy-MM-ddTHH:mm:sszzz")),
                    sitemapNode.Frequency == null ? null : new XElement(
                        xmlns + "changefreq",
                        sitemapNode.Frequency.Value.ToString().ToLowerInvariant()),
                    sitemapNode.Priority == null ? null : new XElement(
                        xmlns + "priority",
                        sitemapNode.Priority.Value.ToString("F1", CultureInfo.InvariantCulture)));
                root.Add(urlElement);
            }

            XDocument document = new XDocument(root);
            return document.ToString();
        }

        public IReadOnlyCollection<SitemapNode> GetSitemapNodes()
        {
          string  lang = ViewBag.lang;
            List<SitemapNode> nodes = new List<SitemapNode>();

            nodes.Add(
                new SitemapNode()
                {
                    Url = "https://smddecoration.com/",
                    
                    Frequency = SitemapFrequency.Monthly,
                    Priority = 1
                });
            nodes.Add(
             new SitemapNode()
             {
                 Url = "https://smddecoration.com/about-us",
                 Frequency = SitemapFrequency.Monthly,
                 Priority = 1
             });
            nodes.Add(
             new SitemapNode()
             {
                 Url = "https://smddecoration.com/contact-us",
                 Frequency = SitemapFrequency.Monthly,
                 Priority = 1
             });
            nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/our-clients",
                Frequency = SitemapFrequency.Monthly,
                Priority = 1
            });
            nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/our-profile",
                Frequency = SitemapFrequency.Monthly,
                Priority = 1
            });
            nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/smd-videos",
                Frequency = SitemapFrequency.Monthly,
                Priority = 1
            });
            nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/career",
                Frequency = SitemapFrequency.Monthly,
                Priority = 1
            });
            nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/faqs",
                Frequency = SitemapFrequency.Daily,
                Priority = 1
            });
            nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/project-category/residential-projects",
                Frequency = SitemapFrequency.Monthly,
                Priority = 1
            });
            nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/project-category/commercial-projects",
                Frequency = SitemapFrequency.Monthly,
                Priority = 1
            });
            nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/projects-as-countries",
                Frequency = SitemapFrequency.Monthly,
                Priority = 1
            }); nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/video-gallery",
                Frequency = SitemapFrequency.Monthly,
                Priority = 1
            }); nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/360-gallery",
                Frequency = SitemapFrequency.Monthly,
                Priority = 1
            });
            nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/projects",
                Frequency = SitemapFrequency.Monthly,
                Priority = 1
            });
            nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/feed/projects",
                Frequency = SitemapFrequency.Daily,
                Priority = 1
            });
            nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/feed/posts",
                Frequency = SitemapFrequency.Daily,
                Priority = 1
            });
            nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/blog",
                Frequency = SitemapFrequency.Monthly,
                Priority = 1
            });
            nodes.Add(new SitemapNode()
            {
                Url = "https://smddecoration.com/categories",
                Frequency = SitemapFrequency.Monthly,
                Priority = 1
            });
            foreach (var project in db.projects.Where(a => a.publish && a.lang == lang).Select(a => new { a.slogan, a.date }).ToList())
            {
                nodes.Add(
                   new SitemapNode()
                   {
                       Url = "https://smddecoration.com/project/" + project.slogan.Replace("_", "-"),
                       Frequency = SitemapFrequency.Monthly,
                       Priority = 0.8,
                       LastModified = project.date
                   });
            }
            foreach (var post in db.posts.Where(a => a.publish && a.lang == lang).Select(a => new { a.slogan, a.date }).ToList())
            {
                nodes.Add(
                   new SitemapNode()
                   {
                       Url = "https://smddecoration.com/post/" + post.slogan.Replace("_", "-"),
                       Frequency = SitemapFrequency.Weekly,
                       Priority = 0.8,
                       LastModified = post.date
                   });
            }
            foreach (var news in db.postcats.Where(a =>  a.lang == lang).Select(a => a.slogan).ToList())
            {
                nodes.Add(
                   new SitemapNode()
                   {
                       Url = "https://smddecoration.com/category/" + news.Replace("_", "-"),
                       Frequency = SitemapFrequency.Daily,
                       Priority = 0.8
                   });
            }

            return nodes;
        }
        [HttpPost]
        [ValidateHeaderAntiForgeryToken]
        public JsonResult addrequests(string message, string phone, string email, string name, string token ,string way)
        {
            smdservices t = new smdservices();
            var status = t.ReCaptchaPassed(token);
            if (status == true)
            {

                //string connectionString2 = WebConfigurationManager.ConnectionStrings["rightway1"].ConnectionString;

                //SqlConnection con2 = new SqlConnection(connectionString2);
                //con2.Open();
                //SqlCommand cmd = new SqlCommand("insert into contactrequests (name,tel,email,message) Values(@name,@tel,@email,@message)", con2);


                //cmd.Parameters.AddWithValue("@name", name);
                //cmd.Parameters.AddWithValue("@message", message);
                //cmd.Parameters.AddWithValue("@tel", phone);
                //cmd.Parameters.AddWithValue("@email", email);


                //cmd.ExecuteNonQuery();
                //con2.Close();

                StreamReader reader = new StreamReader(Server.MapPath("~/lead.html"));


                string readFile = reader.ReadToEnd();

                string myString = "";

                myString = readFile;

                myString = myString.Replace("$$yhya1$$", name);

                myString = myString.Replace("$$yhya4$$", phone);
                myString = myString.Replace("$$yhya5$$", email);
                myString = myString.Replace("$$yhya6$$", way);
            
                myString = myString.Replace("$$yhya7$$",message);






                string subject = "New Request";
                MailMessage mail = new MailMessage();




                mail.From = new MailAddress("<EMAIL>", "SMD");

                string toAddress = email;


                mail.To.Add(toAddress);

                mail.To.Add("<EMAIL>");
                mail.Subject = subject;
                mail.Body = myString.ToString();
                mail.IsBodyHtml = true;



                SmtpClient smtp = new SmtpClient();
                {
                    smtp.Host = "mail.smddecoration.com";
                    System.Net.NetworkCredential netcre = new System.Net.NetworkCredential();
                    netcre.UserName = "<EMAIL>";
                    netcre.Password = "Or5S=8by@:1n_2TC";
                    smtp.UseDefaultCredentials = true;
                    smtp.Credentials = netcre;

                    smtp.Port = 587;




                }


                  smtp.Send(mail);

                return Json(new
                {
                    msg = "ok"
                });


            }
            else
            {
                return Json(new
                {
                    msg = "error"
                });
            }


        }
        [HttpPost]
        [ValidateHeaderAntiForgeryToken]
        public JsonResult addjobrequests(string message, string phone, string email, string name, string token, string way,string gender,string adress,string surname)
        {
            smdservices t = new smdservices();
            var status = t.ReCaptchaPassed(token);
            if (status == true)
            {

                //string connectionString2 = WebConfigurationManager.ConnectionStrings["rightway1"].ConnectionString;

                //SqlConnection con2 = new SqlConnection(connectionString2);
                //con2.Open();
                //SqlCommand cmd = new SqlCommand("insert into contactrequests (name,tel,email,message) Values(@name,@tel,@email,@message)", con2);


                //cmd.Parameters.AddWithValue("@name", name);
                //cmd.Parameters.AddWithValue("@message", message);
                //cmd.Parameters.AddWithValue("@tel", phone);
                //cmd.Parameters.AddWithValue("@email", email);


                //cmd.ExecuteNonQuery();
                //con2.Close();

                StreamReader reader = new StreamReader(Server.MapPath("~/job.html"));


                string readFile = reader.ReadToEnd();

                string myString = "";

                myString = readFile;

                myString = myString.Replace("$$yhya1$$", name);
                myString = myString.Replace("$$yhya2$$", surname);
                myString = myString.Replace("$$yhya3$$", gender);
                myString = myString.Replace("$$yhya4$$", phone);
                myString = myString.Replace("$$yhya5$$", email);
                myString = myString.Replace("$$yhya6$$", way);
                myString = myString.Replace("$$yhya8$$", adress);
                myString = myString.Replace("$$yhya7$$", message);






                string subject = "New Request Job";
                MailMessage mail = new MailMessage();




                mail.From = new MailAddress("<EMAIL>", "SMD");

                string toAddress = email;


                mail.To.Add(toAddress);

                mail.To.Add("<EMAIL>");
                mail.Subject = subject;
                mail.Body = myString.ToString();
                mail.IsBodyHtml = true;



                SmtpClient smtp = new SmtpClient();
                {
                    smtp.Host = "mail.smddecoration.com";
                    System.Net.NetworkCredential netcre = new System.Net.NetworkCredential();
                    netcre.UserName = "<EMAIL>";
                    netcre.Password = "0y=15-5=W:AqiLRw";
                    smtp.UseDefaultCredentials = true;
                    smtp.Credentials = netcre;

                    smtp.Port = 587;




                }


                smtp.Send(mail);

                return Json(new
                {
                    msg = "ok"
                });


            }
            else
            {
                return Json(new
                {
                    msg = "error"
                });
            }


        }

        public ActionResult Index()
        {
            string lang = "ar";
            lang = ViewBag.lang;
           

        

        smdservices t = new smdservices();
            ViewBag.Title = t.pagetitle(1, ViewBag.lang);
          
            ViewBag.OGTitle = t.pageogtitle(1, ViewBag.lang);
            ViewBag.OGDesc = t.pageogdes(1, ViewBag.lang);
            ViewBag.keyword = t.pagekeyword(1, ViewBag.lang);
            t.pagec("1");

            ViewBag.slider = db.sliders.ToList();
            ViewBag.post = (from t1 in db.posts
                            where t1.lang==lang
                            orderby t1.numofvisit 
                            select t1).Take(9);
            ViewBag.category = (from t1 in db.postcats
                            where t1.lang == lang
                                orderby t1.numofvisit 
                                select t1).Take(18);
            ViewBag.refran = (from t1 in db.references
                              where t1.showinhome==true
                              orderby t1.referenceid descending
                            select t1);
            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";

            return View();
        }
        string pattern = "[^a-zA-Z0-9\\u0600-\\u06FF\\u0750-\\u077F\\u08A0-\\u08FF\\uFB50-\\uFDFD\\uFE70-\\uFEFF?\\s]";
        string replacement = "";
        public ActionResult faqsall(int? page)
        {
            string lang = "ar";
            lang = ViewBag.lang;




            smdservices t = new smdservices();
            ViewBag.Title = t.pagetitle(13, ViewBag.lang) + "سؤال وجواب " + page;

            ViewBag.OGTitle = t.pageogtitle(13, ViewBag.lang) + "سؤال وجواب " + page;
            ViewBag.OGDesc = t.pageogdes(13, ViewBag.lang) + "سؤال وجواب " + page;
            ViewBag.keyword = t.pagekeyword(13, ViewBag.lang) + "سؤال وجواب " + page;
            t.pagec("13");


            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";
            var ques = db.faqs.OrderBy(a => a.arrange);
            string schme = "";
            int i = 0;
            int j = db.faqs.Count();

            foreach (var dr in ques)
            {
                string questionsname = t.getvalue("ar", dr.question);
                string questionsanswers = t.getvalue("ar", dr.answer);
                schme += "{\"@type\": \"Question\",\"name\": \"" + Regex.Replace(questionsname, pattern, replacement) + "\",\"acceptedAnswer\": {\"@type\": \"Answer\",\"text\": \"" + Regex.Replace(questionsanswers, pattern, replacement) + "\"} }";
                i++;
                if (i < j)
                {
                    schme += ",";
                }

            }
            ViewBag.schme = schme;
            int pageSize = 15;
            int pageNumber = (page ?? 1);


           
            return View(db.faqs.OrderByDescending(a => a.faqsid).ToList().ToPagedList(pageNumber, pageSize));
        }
        public ActionResult profile()
        {
            string lang = "ar";
            lang = ViewBag.lang;




            smdservices t = new smdservices();
            ViewBag.Title = t.pagetitle(12, ViewBag.lang);

            ViewBag.OGTitle = t.pageogtitle(12, ViewBag.lang);
            ViewBag.OGDesc = t.pageogdes(12, ViewBag.lang);
            ViewBag.keyword = t.pagekeyword(12, ViewBag.lang);
            t.pagec("12");

         
            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";

            return View();
        }

        public ActionResult About()
        {
            smdservices t = new smdservices();
            ViewBag.Title = t.pagetitle(3, ViewBag.lang);

            ViewBag.OGTitle = t.pageogtitle(3, ViewBag.lang);
            ViewBag.OGDesc = t.pageogdes(3, ViewBag.lang);
            ViewBag.keyword = t.pagekeyword(3, ViewBag.lang);
            ViewBag.listaw = db.awards.ToList();
            ViewBag.pdf = db.documents.Where(a=>a.year=="2022").ToList();

            t.pagec("3");

            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";

            return View();
        }

        public ActionResult Contact()
        {
            smdservices t = new smdservices();
            ViewBag.Title = t.pagetitle(2, ViewBag.lang);

            ViewBag.OGTitle = t.pageogtitle(2, ViewBag.lang);
            ViewBag.OGDesc = t.pageogdes(2, ViewBag.lang);
            ViewBag.keyword = t.pagekeyword(2, ViewBag.lang);
            t.pagec("2");


            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";

            return View();
        }
        public ActionResult hr()
        {
            smdservices t = new smdservices();
            ViewBag.Title = t.pagetitle(6, ViewBag.lang);

            ViewBag.OGTitle = t.pageogtitle(6, ViewBag.lang);
            ViewBag.OGDesc = t.pageogdes(6, ViewBag.lang);
            ViewBag.keyword = t.pagekeyword(6, ViewBag.lang);

            t.pagec("6");

            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";

            return View();
        }
        public ActionResult privacy()
        {
            smdservices t = new smdservices();
            ViewBag.Title = "سياسة الخصوصية";

            ViewBag.OGTitle = "سياسة الخصوصية";
            ViewBag.OGDesc = "  سياسة الخصوصية لموقع SMD Decoration";
            ViewBag.keyword = "سياسة الخصوصية";



            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";

            return View();
        }
        public ActionResult blog(int? page)
        {
            string lang = "ar";
            lang = ViewBag.lang;
            smdservices t = new smdservices();
            ViewBag.Title = t.pagetitle(4, ViewBag.lang)+"اخبار ومدونة "+ page;

            ViewBag.OGTitle = t.pageogtitle(4, ViewBag.lang) + "اخبار ومدونة " + page;
            ViewBag.OGDesc = t.pageogdes(4, ViewBag.lang) + "اخبار ومدونة " + page;
            ViewBag.keyword = t.pagekeyword(4, ViewBag.lang);
            t.pagec("4");


            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";
            int pageSize = 15;
            int pageNumber = (page ?? 1);
           

            return View(db.posts.Where(a => a.lang == lang && a.publish).OrderByDescending(a => a.arrange).ToList().ToPagedList(pageNumber, pageSize));
        }
        public ActionResult categories(int? page)
        {
            string lang = "ar";
            lang = ViewBag.lang;
            smdservices t = new smdservices();
            ViewBag.Title = t.pagetitle(14, ViewBag.lang) + "الاقسام والفئات في SMD Decoration اسطنبول تركيا " + page;

            ViewBag.OGTitle = t.pageogtitle(14, ViewBag.lang) + "الاقسام والفئات في SMD Decoration اسطنبول تركيا " + page;
            ViewBag.OGDesc = t.pageogdes(14, ViewBag.lang) + "الاقسام والفئات في SMD Decoration اسطنبول تركيا " + page;
            ViewBag.keyword = t.pagekeyword(14, ViewBag.lang);
            t.pagec("14");


            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";
            int pageSize = 15;
            int pageNumber = (page ?? 1);


            return View(db.postcats.Where(a => a.lang == lang ).OrderBy(a => a.postcatid).ToList().ToPagedList(pageNumber, pageSize));
        }
        public ActionResult tags(int? page,string id)
        {
            if (id == null)
            {
                Response.Clear(); // Clear the response to remove any previous content
                Response.Status = "301 Moved Permanently"; // Set the status code to 301
                Response.StatusCode = 301;
                ViewBag.st = "1";
                ViewBag.url = "https://smddecoration.com/blog";
                Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                Response.End();
            }
            id = id.Replace(":", "");
            string lang = "ar";
            lang = ViewBag.lang;
            smdservices t = new smdservices();
          
            t.pagec("4");


            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";
            int pageSize = 15;
            int pageNumber = (page ?? 1);

            if (id != null)
            {
                ViewBag.tag = id;
                ViewBag.Title = id;

                ViewBag.OGTitle = id + " | " + t.pagetitle(1, ViewBag.lang); 
                ViewBag.OGDesc = id+ t.pageogdes(1, ViewBag.lang);
                ViewBag.keyword = id+t.pagekeyword(1, ViewBag.lang);
                return View(db.posts.Where(a => a.lang == lang && a.publish&&a.keywords.Contains(id)).OrderByDescending(a => a.arrange).ToList().ToPagedList(pageNumber, pageSize));
            }
            else
            {
                ViewBag.tag = "";
                ViewBag.Title = t.pagetitle(4, ViewBag.lang);

                ViewBag.OGTitle = t.pageogtitle(4, ViewBag.lang);
                ViewBag.OGDesc = t.pageogdes(4, ViewBag.lang);
                ViewBag.keyword = t.pagekeyword(4, ViewBag.lang);
                return View(db.posts.Where(a => a.lang == lang && a.publish).OrderByDescending(a => a.arrange).ToList().ToPagedList(pageNumber, pageSize));
            }
        }
        public ActionResult category(int? page, string id)
        {
            if (id == null)
            {
                Response.Clear(); // Clear the response to remove any previous content
                Response.Status = "301 Moved Permanently"; // Set the status code to 301
                Response.StatusCode = 301;
                ViewBag.st = "1";
                ViewBag.url = "https://smddecoration.com/categories";
                Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                Response.End();
            }
            if (id.Contains("اخبارنا") || id.Contains("blog") || id.Contains("post"))
            {
                Response.Clear(); // Clear the response to remove any previous content
                Response.Status = "301 Moved Permanently"; // Set the status code to 301
                Response.StatusCode = 301;
                ViewBag.st = "1";
                ViewBag.url = "https://smddecoration.com/blog";
                Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                Response.End();
            }
            if (id.Contains("projects"))
            {
                Response.Clear(); // Clear the response to remove any previous content
                Response.Status = "301 Moved Permanently"; // Set the status code to 301
                Response.StatusCode = 301;
                ViewBag.st = "1";
                ViewBag.url = "https://smddecoration.com/projects";
                Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                Response.End();
            }
            if (id.Contains("career"))
            {
                Response.Clear(); // Clear the response to remove any previous content
                Response.Status = "301 Moved Permanently"; // Set the status code to 301
                Response.StatusCode = 301;
                ViewBag.st = "1";
                ViewBag.url = "https://smddecoration.com/career";
                Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                Response.End();
            }
            if (id.Contains("contact-us")){
                Response.Clear(); // Clear the response to remove any previous content
                Response.Status = "301 Moved Permanently"; // Set the status code to 301
                Response.StatusCode = 301;
                ViewBag.st = "1";
                ViewBag.url = "https://smddecoration.com/our-profile";
                Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                Response.End();
            }
            if (id.Contains("about-us"))
            {
                Response.Clear(); // Clear the response to remove any previous content
                Response.Status = "301 Moved Permanently"; // Set the status code to 301
                Response.StatusCode = 301;
                ViewBag.st = "1";
                ViewBag.url = "https://smddecoration.com/about-us";
                Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                Response.End();
            }
            if (id.Contains("faqs"))
            {
                Response.Clear(); // Clear the response to remove any previous content
                Response.Status = "301 Moved Permanently"; // Set the status code to 301
                Response.StatusCode = 301;
                ViewBag.st = "1";
                ViewBag.url = "https://smddecoration.com/faqs";
                Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                Response.End();
            }
            
            if (id.Contains("video-gallery"))
            {
                Response.Clear(); // Clear the response to remove any previous content
                Response.Status = "301 Moved Permanently"; // Set the status code to 301
                Response.StatusCode = 301;
                ViewBag.st = "1";
                ViewBag.url = "https://smddecoration.com/video-gallery";
                Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                Response.End();
            }
            if (id.Contains("الديكور") || id.Contains("افكار-هندسية") || id.Contains("دهانات") || id.Contains("ورق-الحائط"))
            {
                id = "Decorations-and-interior-design";
            }
            if (id.Contains("مطابخ"))
            {
                id = "Kitchen-decoration-and-kitchen-design";
            }
            string lang = "ar";
            lang = ViewBag.lang;
            ViewBag.id1 = id;
           smdservices t = new smdservices();

           int idd = (from rol in db.postcats
                    where rol.slogan == id

                    select rol.postcatid).SingleOrDefault();
            string guidd = (from rol in db.postcats
                       where rol.slogan == id

                       select rol.guid).SingleOrDefault();
            int idd9 = (from rol in db.blocks
                       where rol.guid == guidd

                       select rol.blocksid).SingleOrDefault();

            t.postcats(idd.ToString());
            ViewBag.id9 = idd9;
            ViewBag.pdf = db.documents.Where(a => a.guid == guidd&&a.lang== "gdrive").ToList();

            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";
            int pageSize = 15;
            int pageNumber = (page ?? 1);

            if (idd != null)
            {
               
                ViewBag.id1n = t.getvalue(lang, (from rol in db.postcats
                                                 where rol.slogan == id

                                                 select rol.title).SingleOrDefault());
                ViewBag.tag = ViewBag.id1n;
                ViewBag.Title = ViewBag.id1n + "ديكور وتصميم داخلي اسطنبول تركيا" + page;
                ViewBag.OGTitle = t.postcatogtitle(idd, ViewBag.lang) + "ديكور وتصميم داخلي اسطنبول تركيا" + page;
                ViewBag.OGDesc = t.postcatogdes(idd, ViewBag.lang)+ "ديكور وتصميم داخلي اسطنبول تركيا" + page;
                ViewBag.keyword = t.postcatkeyword(idd, ViewBag.lang);
                return View(db.posts.Where(a => a.lang == lang && a.publish && a.postcatid==idd).OrderByDescending(a => a.arrange).ToList().ToPagedList(pageNumber, pageSize));
            }
            else
            {
                Response.StatusCode = 301;

                return RedirectPermanent("https://smddecoration.com/categories");
            }
        }
        public ActionResult ourwork(int? page)
        {
            string lang = "ar";
            lang = ViewBag.lang;
            smdservices t = new smdservices();
            ViewBag.Title = t.pagetitle(5, ViewBag.lang) + "مشاريع مودرن " + page;

            ViewBag.OGTitle = t.pageogtitle(5, ViewBag.lang) + "مشاريع مودرن " + page;
            ViewBag.OGDesc = t.pageogdes(5, ViewBag.lang) + "مشاريع مودرن " + page;
            ViewBag.keyword = t.pagekeyword(5, ViewBag.lang);

            t.pagec("5");
            t.pagec("8");

            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";
            int pageSize = 15;
            int pageNumber = (page ?? 1);
            return View(db.projects.Where(a => a.lang == lang && a.publish).OrderByDescending(a => a.arrange).ToList().ToPagedList(pageNumber, pageSize));
        }
        public ActionResult refrencesall(int? page)
        {
            string lang = "ar";
            lang = ViewBag.lang;
            smdservices t = new smdservices();
            ViewBag.Title = t.pagetitle(11, ViewBag.lang) + "عملاؤنا  " + page;

            ViewBag.OGTitle = t.pageogtitle(11, ViewBag.lang) + "عملاؤنا  " + page;
            ViewBag.OGDesc = t.pageogdes(11, ViewBag.lang) + "عملاؤنا  " + page;
            ViewBag.keyword = t.pagekeyword(11, ViewBag.lang);

            t.pagec("11");

            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";
            int pageSize = 15;
            int pageNumber = (page ?? 1);
            return View(db.references.OrderByDescending(a => a.referenceid).ToList().ToPagedList(pageNumber, pageSize));
        }
        // GET: projects/Details/5
        public async Task<ActionResult> projectDetails(string id)
        {
            string lang = ViewBag.lang;

            if (id=="contact-us")
            {
        
                Response.StatusCode = 301;
           
                return RedirectPermanent("https://smddecoration.com/contact-us");
            }
            var topcourse = db.projects.Where(a =>( a.slogan == id||a.oldslogan==id || a.oldslogan1 == id) &&a.lang==lang).SingleOrDefault();

            if (topcourse == null||id==null)
            {
                Response.StatusCode = 301;
                ViewBag.st = "1";
                ViewBag.url = "https://smddecoration.com/projects";
                Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                Response.End(); // End the response
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            ViewBag.slider = db.galleries.Where(a => a.guid == topcourse.guid).ToList();

            SqlConnection conn;
            SqlCommand cmd101;
            //connect to the db
            conn = new SqlConnection(WebConfigurationManager.ConnectionStrings["smd"].ConnectionString);

            //the sql command to increment hits by 1
            cmd101 = new SqlCommand("UPDATE projects SET numofvisit = numofvisit+1 WHERE projectsid=@id", conn);
            cmd101.CommandType = CommandType.Text;

            //update where Name is 'Default' which corresponds to this page
            cmd101.Parameters.AddWithValue("@id", topcourse.projectsid);

            using (conn)
            {
                //open the connection
                conn.Open();
                //send the query
                cmd101.ExecuteNonQuery();
            }
           
            var topcourses = db.projects.Where(a =>( a.slogan == id || a.oldslogan == id || a.oldslogan1 == id) && a.lang == lang).ToList();
           // ViewBag.projectsss = db.projects.OrderBy(x => Guid.NewGuid()).Take(3);
        
        
    
      
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            //   projects projects = await db.projectss.FindAsync(id);
            //    if (projects == null)
            //    {
            //    return HttpNotFound();
            //  }
            return View(topcourses);
        }
        public ActionResult newsdetials(string id)
        {
            string lang = ViewBag.lang;
            if (id == "contact-us")
            {
                
                Response.StatusCode = 301;

                return RedirectPermanent("https://smddecoration.com/contact-us");
            }
            var topcourses = db.posts.Where(a => (a.slogan == id || a.oldslogan == id || a.oldslogan1 == id) && a.lang == lang).SingleOrDefault();
            if (topcourses == null || id == null)
            {
                var topcourse8 = db.projects.Where(a => (a.slogan == id || a.oldslogan == id || a.oldslogan1 == id) && a.lang == lang).SingleOrDefault();

                if (topcourse8 != null)
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/project/" + id;
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                else
                {
                    return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
                }
            }






            var topcourse = db.posts.Where(a => (a.slogan == id || a.oldslogan == id || a.oldslogan1 == id) && a.lang == lang).ToList();
         
            SqlConnection conn;
            SqlCommand cmd101;
            //connect to the db
            conn = new SqlConnection(WebConfigurationManager.ConnectionStrings["smd"].ConnectionString);

            //the sql command to increment hits by 1
            cmd101 = new SqlCommand("UPDATE posts SET numofvisit = numofvisit+1 WHERE postsid=@id", conn);
            cmd101.CommandType = CommandType.Text;

            //update where Name is 'Default' which corresponds to this page
            cmd101.Parameters.AddWithValue("@id", topcourses.postsid);

            using (conn)
            {
                //open the connection
                conn.Open();
                //send the query
                cmd101.ExecuteNonQuery();
            }


            string tree = "";
            string content8 = topcourses.content;
            List<listofh> text = new List<listofh>();
            var htmlDocument = new HtmlDocument();
            htmlDocument.LoadHtml(topcourses.content);
            var xpath = "//*[ self::h2 or self::h3 ]";
            int y = 0;
            var yhy = htmlDocument.DocumentNode.SelectNodes(xpath);
            if (yhy != null)
            {
                foreach (var node in htmlDocument.DocumentNode.SelectNodes(xpath))
                {
                    y++;
                    text.Add(new listofh
                    {
                        item = node.InnerHtml,
                        item1 = node.Name,
                        arrange = y
                    });

                }
                foreach (var node in text)
                {
                    string oldyy;
                    string yy;
                    if (node.item1 == "h2")
                    {
                        oldyy = "<" + node.item1 + ">" + node.item + "</" + node.item1 + ">";

                    }
                    else
                    {
                        oldyy = "<" + node.item1 + ">" + node.item + "</" + node.item1 + ">";
                    }
                    if (node.item1 == "h2")
                    {
                        yy = "<" + node.item1 + " id=\"node" + node.arrange + "\" >" + node.item + "</" + node.item1 + ">";
                    }
                    else
                    {
                        yy = "<" + node.item1 + " id=\"node" + node.arrange + "\">" + node.item + "</" + node.item1 + ">";
                    }
                    if (node.item1 == "h2")
                    {
                        tree += "<li style=\"margin-bottom: 10px;\"><a href=\"#\" OnClick=\"move(" + node.arrange + ");\"    >" + node.item + "</a></li>";
                    }
                    else
                    {
                        tree += "<li style=\"margin-bottom: 10px;\"><a href=\"#\"  OnClick=\"move(" + node.arrange + ");\" style=\"color:#fff;\"  >" + node.item + "</a></li>";
                    }
                    content8 = content8.Replace(oldyy, yy);
                }
                ViewBag.des= content8;
                ViewBag.tree = tree;
            }
            else
            {
                ViewBag.des = content8;
                ViewBag.tree = tree;

            }
            smdservices t = new smdservices();
            var ques = db.faqs.Where(a=>a.ogtitle==topcourses.guid).OrderBy(a => a.faqsid);
            string schme = "";
            int i = 0;
            int j = db.faqs.Where(a => a.ogtitle == topcourses.guid).Count();
            ViewBag.count = j;
            foreach (var dr in ques)
            {
                string questionsname = t.getvalue("ar", dr.question);
                string questionsanswers = t.getvalue("ar", dr.answer);
                schme += "{\"@type\": \"Question\",\"name\": \"" + Regex.Replace(questionsname, pattern, replacement) + "\",\"acceptedAnswer\": {\"@type\": \"Answer\",\"text\": \"" + Regex.Replace(questionsanswers, pattern, replacement) + "\"} }";
                i++;
                if (i < j)
                {
                    schme += ",";
                }

            }
            ViewBag.schme = schme;
            ViewBag.list= db.faqs.Where(a => a.ogtitle == topcourses.guid).OrderBy(a => a.faqsid).ToList();
            return View(topcourse);
        }
        public ActionResult newsdetialsc(string id,string id1)
        {
            string lang = ViewBag.lang;
            if (id == "contact-us")
            {

                Response.StatusCode = 301;

                return RedirectPermanent("https://smddecoration.com/contact-us");
            }
            var topcourses = db.posts.Where(a => (a.slogan == id || a.oldslogan == id || a.oldslogan1 == id) && a.lang == lang).SingleOrDefault();
            if (topcourses == null || id == null)
            {
                var topcourse8 = db.projects.Where(a => (a.slogan == id || a.oldslogan == id || a.oldslogan1 == id) && a.lang == lang).SingleOrDefault();

                if (topcourse8 != null)
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/project/" + id;
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                else
                {
                    return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
                }
            }






            var topcourse = db.posts.Where(a => (a.slogan == id || a.oldslogan == id || a.oldslogan1 == id) && a.lang == lang).ToList();

            SqlConnection conn;
            SqlCommand cmd101;
            //connect to the db
            conn = new SqlConnection(WebConfigurationManager.ConnectionStrings["smd"].ConnectionString);

            //the sql command to increment hits by 1
            cmd101 = new SqlCommand("UPDATE posts SET numofvisit = numofvisit+1 WHERE postsid=@id", conn);
            cmd101.CommandType = CommandType.Text;

            //update where Name is 'Default' which corresponds to this page
            cmd101.Parameters.AddWithValue("@id", topcourses.postsid);

            using (conn)
            {
                //open the connection
                conn.Open();
                //send the query
                cmd101.ExecuteNonQuery();
            }


            string tree = "";
            string content8 = topcourses.content;
            List<listofh> text = new List<listofh>();
            var htmlDocument = new HtmlDocument();
            htmlDocument.LoadHtml(topcourses.content);
            var xpath = "//*[ self::h2 or self::h3 ]";
            int y = 0;
            var yhy = htmlDocument.DocumentNode.SelectNodes(xpath);
            if (yhy != null)
            {
                foreach (var node in htmlDocument.DocumentNode.SelectNodes(xpath))
                {
                    y++;
                    text.Add(new listofh
                    {
                        item = node.InnerHtml,
                        item1 = node.Name,
                        arrange = y
                    });

                }
                foreach (var node in text)
                {
                    string oldyy;
                    string yy;
                    if (node.item1 == "h2")
                    {
                        oldyy = "<" + node.item1 + ">" + node.item + "</" + node.item1 + ">";

                    }
                    else
                    {
                        oldyy = "<" + node.item1 + ">" + node.item + "</" + node.item1 + ">";
                    }
                    if (node.item1 == "h2")
                    {
                        yy = "<" + node.item1 + " id=\"node" + node.arrange + "\" >" + node.item + "</" + node.item1 + ">";
                    }
                    else
                    {
                        yy = "<" + node.item1 + " id=\"node" + node.arrange + "\">" + node.item + "</" + node.item1 + ">";
                    }
                    if (node.item1 == "h2")
                    {
                        tree += "<li style=\"margin-bottom: 10px;\"><a href=\"#\" OnClick=\"move(" + node.arrange + ");\"    >" + node.item + "</a></li>";
                    }
                    else
                    {
                        tree += "<li style=\"margin-bottom: 10px;\"><a href=\"#\"  OnClick=\"move(" + node.arrange + ");\" style=\"color:#fff;\"  >" + node.item + "</a></li>";
                    }
                    content8 = content8.Replace(oldyy, yy);
                }
                ViewBag.des = content8;
                ViewBag.tree = tree;
            }
            else
            {
                ViewBag.des = content8;
                ViewBag.tree = tree;

            }
            ViewBag.id1 = id1;
         
            smdservices t = new smdservices();
            ViewBag.id1n = t.getvalue(lang, (from rol in db.postcats
                                    where rol.slogan == id1

                                    select rol.title).SingleOrDefault());
            var ques = db.faqs.Where(a => a.ogtitle == topcourses.guid).OrderBy(a => a.faqsid);
            string schme = "";
            int i = 0;
            int j = db.faqs.Where(a => a.ogtitle == topcourses.guid).Count();
            ViewBag.count = j;
            foreach (var dr in ques)
            {
                string questionsname = t.getvalue("ar", dr.question);
                string questionsanswers = t.getvalue("ar", dr.answer);
                schme += "{\"@type\": \"Question\",\"name\": \"" + Regex.Replace(questionsname, pattern, replacement) + "\",\"acceptedAnswer\": {\"@type\": \"Answer\",\"text\": \"" + Regex.Replace(questionsanswers, pattern, replacement) + "\"} }";
                i++;
                if (i < j)
                {
                    schme += ",";
                }

            }
            ViewBag.schme = schme;
            ViewBag.list = db.faqs.Where(a => a.ogtitle == topcourses.guid).OrderBy(a => a.faqsid).ToList();
            return View(topcourse);
        }
        public class listofh
        {



            public string item { get; set; }
            public string item1 { get; set; }
            public int arrange { get; set; }

        }
        
                 public ActionResult smdvideos(int? page,string id)
        {
            string lang = "ar";
            lang = ViewBag.lang;
            string idd = id;
            if (id == null)
            {
                idd = "a-minute-idea";
            }
            int kk = db.videocategories.Where(a => a.slogan == idd).Select(a => a.videocategoryid).SingleOrDefault();
            smdservices t = new smdservices();
            ViewBag.Title = t.vidcattitle(kk, ViewBag.lang);

            ViewBag.OGTitle = t.vidcatogtitle(kk, ViewBag.lang);
            ViewBag.OGDesc = t.vidcatogdes(kk, ViewBag.lang);
            ViewBag.keyword = t.vidcatkeyword(kk, ViewBag.lang);

            t.vidcat(kk.ToString());

            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";

            int pageSize = 15;
            int pageNumber = (page ?? 1);
            return View(db.videossmds.Where(a => a.lang == lang&& a.videocategoryid == kk).OrderByDescending(a => a.videossmdid).ToList().ToPagedList(pageNumber, pageSize));

        }
        public ActionResult repro(int? page)
        {
           
            string lang = "ar";
            lang = ViewBag.lang;
            smdservices t = new smdservices();
            ViewBag.Title = t.project_categorietitle(1, ViewBag.lang) + "مشاريع سكنية " + page;

            ViewBag.OGTitle = t.project_categorieogtitle(1, ViewBag.lang) + "مشاريع سكنية " + page;

            ViewBag.OGDesc = t.project_categorieogdes(1, ViewBag.lang) + "مشاريع سكنية " + page;

            ViewBag.keyword = t.project_categoriekeyword(1, ViewBag.lang) + "مشاريع سكنية " + page;

            t.procat("1");


            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";

            int pageSize = 15;
            int pageNumber = (page ?? 1);
            return View(db.projects.Where(a => a.lang == lang && a.publish&&a.project_categoriesid==1).OrderByDescending(a => a.projectsid).ToList().ToPagedList(pageNumber, pageSize));

        }
        public ActionResult copro(int? page)
        {
            string lang = "ar";
            lang = ViewBag.lang;
            smdservices t = new smdservices();
            ViewBag.Title = t.project_categorietitle(2, ViewBag.lang) + "مشاريع تجارية " + page;


            ViewBag.OGTitle = t.project_categorieogtitle(2, ViewBag.lang) + "مشاريع تجارية " + page;
            ViewBag.OGDesc = t.project_categorieogdes(2, ViewBag.lang) + "مشاريع تجارية " + page;
            ViewBag.keyword = t.project_categoriekeyword(2, ViewBag.lang) + "مشاريع تجارية " + page;

            t.procat("2");

            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";

            int pageSize = 15;
            int pageNumber = (page ?? 1);
            return View(db.projects.Where(a => a.lang == lang && a.publish && a.project_categoriesid == 2).OrderByDescending(a => a.projectsid).ToList().ToPagedList(pageNumber, pageSize));

        }
        public ActionResult probycountry()
        {
            smdservices t = new smdservices();
            ViewBag.Title = t.pagetitle(7, ViewBag.lang);

            ViewBag.OGTitle = t.pageogtitle(7, ViewBag.lang);
            ViewBag.OGDesc = t.pageogdes(7, ViewBag.lang);
            ViewBag.keyword = t.pagekeyword(7, ViewBag.lang);

            t.pagec("7");

            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";
            string markers = "[";
            string conString = ConfigurationManager.ConnectionStrings["smd"].ConnectionString;
            SqlCommand cmd = new SqlCommand("SELECT * FROM projects where lang='"+ViewBag.lang+"' and publish=1");
            using (SqlConnection con = new SqlConnection(conString))
            {
                cmd.Connection = con;
                con.Open();
                using (SqlDataReader sdr = cmd.ExecuteReader())
                {
                    while (sdr.Read())
                    { string image = t.getimagewithtype(sdr["guid"].ToString(), "project");
                        string title = sdr["title"].ToString();
                        int tcatid = Convert.ToInt32(sdr["project_categoriesid"]);
                        string cat = t.projectcategory(tcatid, ViewBag.lang); 
                        string link = sdr["slogan"].ToString();
                        markers += "{";
                    
                        markers += string.Format("lat: {0},", sdr["Latitude"]);
                        markers += string.Format("lon: {0},", sdr["Longitude"]);
                        markers += string.Format("title: '{0}',", title);
                        markers += string.Format("html: '{0}',", "<div class=\"haritaacilan\"><div style=\"background-image: url("+image+")\"></div><h1>"+ title+"</h1><p> "+cat+"</p><a href=\"/projects/"+link+"\">"+Resources.Resource.view_details + "<i class=\"fi flaticon-next\"></i></a></div>");
                        markers += string.Format("icon: {0},", "haritaIconu");
                        markers += string.Format("animation: {0}", "google.maps.Animation.DROP");
                        markers += "},";
                    }
                }
                con.Close();
            }

            markers += "];";
            ViewBag.Markers = markers;

            return View();
        }
        public ActionResult vedpro(int? page)
        {
            string lang = "ar";
            lang = ViewBag.lang;
            smdservices t = new smdservices();
            ViewBag.Title = t.pagetitle(9, ViewBag.lang) + "فيديوهات " + page;

            ViewBag.OGTitle = t.pageogtitle(9, ViewBag.lang) + "فيديوهات " + page;
            ViewBag.OGDesc = t.pageogdes(9, ViewBag.lang) + "فيديوهات " + page;
            ViewBag.keyword = t.pagekeyword(9, ViewBag.lang) + "فيديوهات " + page;

            t.pagec("9");

            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";
            int pageSize = 15;
            int pageNumber = (page ?? 1);
            return View(db.videos.Where(a => a.lang == lang ).OrderByDescending(a => a.videosid).ToList().ToPagedList(pageNumber, pageSize));

        }
        public ActionResult g360pro(int? page)
        {
            string lang = "ar";
            lang = ViewBag.lang;
            smdservices t = new smdservices();
            ViewBag.Title = t.pagetitle(10, ViewBag.lang) + "عروض ثري دي " + page;

            ViewBag.OGTitle = t.pageogtitle(10, ViewBag.lang) + "عروض ثري دي " + page;
            ViewBag.OGDesc = t.pageogdes(10, ViewBag.lang) + "عروض ثري دي " + page;
            ViewBag.keyword = t.pagekeyword(10, ViewBag.lang) + "عروض ثري دي " + page;
            t.pagec("10");


            ViewBag.OGImage = "https://smddecoration.com/wp-content/uploads/2022/03/cropped-logo-ne.png";

            int pageSize = 15;
            int pageNumber = (page ?? 1);
            return View(db.g360s.Where(a => a.lang == lang).OrderByDescending(a => a.g360id).ToList().ToPagedList(pageNumber, pageSize));
        }
    }
}