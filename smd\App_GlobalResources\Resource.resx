<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="360_gallery" xml:space="preserve">
    <value>معرض °360</value>
  </data>
  <data name="about-nasa" xml:space="preserve">
    <value>نسعى</value>
  </data>
  <data name="about-nasa2" xml:space="preserve">
    <value>دائما للافضل</value>
  </data>
  <data name="about_slogan" xml:space="preserve">
    <value>نرسم لكم أحلامكم بتصاميم تجمع &lt;span&gt; مابين الأبداع والحداثة &lt;/span&gt;</value>
  </data>
  <data name="address" xml:space="preserve">
    <value>العنوان</value>
  </data>
  <data name="address_title" xml:space="preserve">
    <value>العنوان</value>
  </data>
  <data name="all_projects" xml:space="preserve">
    <value>جميع المشاريع</value>
  </data>
  <data name="all_references" xml:space="preserve">
    <value>جميع العملاء</value>
  </data>
  <data name="apply_now" xml:space="preserve">
    <value>قدم الآن</value>
  </data>
  <data name="awards_received" xml:space="preserve">
    <value>الجوائز التي حصلنا عليها</value>
  </data>
  <data name="before_after" xml:space="preserve">
    <value>قبل بعد</value>
  </data>
  <data name="blog" xml:space="preserve">
    <value>أخبارنا</value>
  </data>
  <data name="career_text" xml:space="preserve">
    <value>مجموعة SMD تؤمن بأن الموظفين الناجحين هم مفتاح نجاح الشركات. لذلك، نحرص على جذب المواهب المتميزة والمتطلعة. في مجموعة SMD، نركز على تطوير طاقمنا الوظيفي والارتقاء بالشركة إلى أعلى مستويات الأداء.

نحن نسعى دائمًا لاكتشاف المواهب الواعدة وتطوير قدراتها لتحقيق تطلعاتنا وأهدافنا المشتركة. نحن نسعى أيضًا لتعزيز فريقنا بحاملي الخبرات والمهارات الاحترافية الذين يساهمون في تطوير عملنا.

إذا كنت تمتلك الخبرة في مجال عملنا وتتمتع بالطموح وروح المنافسة الشريفة، فقد تكون جزءًا من فريق مجموعة SMD. يُرجى ملء استمارة التوظيف واختيار الوظيفة المناسبة لخبرتك ومهاراتك. يُرجى مراجعة شروط التقدم للوظائف الشاغرة لدينا.

رؤيتنا في التوظيف تركز على العمل كفريق واحد واحترام حقوق الموظفين وتطبيق المعايير الإنسانية والأخلاقية في التعامل معهم. يجب أن يتمتع الموظف الناجح باللباقة والأخلاق والقيم الرفيعة لتقديم أفضل خدمة لعملائنا الكرام. نحن نهتم بجذب المواهب المتميزة والكفاءات ذات الخبرة، حيث نهدف دائمًا للعمل مع أرقى الأشخاص للمحافظة وتحسين سمعتنا وتقديم خدماتنا المميزة.

انضم إلى فريق SMD وكن جزءًا من رحلتنا في تحقيق أفضل تصميم وديكور للمطابخ والديكور الداخلي، فنحن نسعى</value>
  </data>
  <data name="career_title" xml:space="preserve">
    <value>&lt;h4&gt;يرجى ملئ &lt;span&gt;الحقول&lt;/span&gt; للتقديم على وظيفة&lt;/h4&gt;</value>
  </data>
  <data name="click_to_view" xml:space="preserve">
    <value>انقر لعرض الوثيقة بصيغة pdf</value>
  </data>
  <data name="company_name" xml:space="preserve">
    <value>SMD DECORATION</value>
  </data>
  <data name="contact_form_text" xml:space="preserve">
    <value>فريق عمل SMD GROUP معكم على مدار الساعة. نحن معكم من التصميم وحتى تسليم المفتاح. نفخر بأن نكون واحدة من أفضل شركات الديكور في تركيا. فريقنا متخصص ومحترف في مجال التصميم الداخلي والديكور. نقدم خدماتنا لتصميم وتنفيذ مشاريع الديكور الداخلي للمنازل والشقق والفيلات والمكاتب والمحلات التجارية والمشاريع الكبيرة الأخرى. نحن نهتم بتفاصيل المشروع ونسعى لتحقيق رؤية العميل بأفضل شكل ممكن. اعتمادًا على احتياجاتك وذوقك الخاص، نقدم تصاميم مبتكرة وعصرية تعكس أسلوبك الفريد وتلبي احتياجاتك الوظيفية. سواء كنت تبحث عن تصميم مطبخ مميز، أو تصميم داخلي شامل للمنزل، يمكننا تحقيق ذلك لك. لا تتردد في التواصل معنا عبر الوتساب أو ملء الاستمارة أدناه. سنكون سعداء بمساعدتك في تحويل أفكارك إلى حقيقة ملموسة.</value>
  </data>
  <data name="contact_form_title" xml:space="preserve">
    <value />
  </data>
  <data name="contact_text" xml:space="preserve">
    <value>هناك حقيقة مثبتة منذ زمن طويل وهي أن المحتوى المقروء لصفحة ما سيلهي القارئ عن التركيز على الشكل الخارجي للنص أو شكل توضع الفقرات في الصفحة التي يقرأها. ولذلك يتم استخدام طريقة لوريم إيبسوم لأنها تعطي توزيعاَ طبيعياَ -إلى حد ما- للأحرف عوضاً عن استخدام "هنا يوجد محتوى نصي، هنا يوجد محتوى نصي" فتجعلها تبدو (أي الأحرف) وكأنها نص مقروء. العديد من برامح النشر المكتبي وبرامح تحرير صفحات الويب تستخدم لوريم إيبسوم بشكل إفتراضي كنموذج عن النص، وإذا قمت بإدخالأي محرك </value>
  </data>
  <data name="contact_title" xml:space="preserve">
    <value>يمكنك أن تجد بعض الأمثلة على</value>
  </data>
  <data name="contactus" xml:space="preserve">
    <value>اتصل بنا</value>
  </data>
  <data name="date" xml:space="preserve">
    <value>تاريخ</value>
  </data>
  <data name="date_of_birth" xml:space="preserve">
    <value>تاريخ الولادة</value>
  </data>
  <data name="email" xml:space="preserve">
    <value>البريد الإلكتروني</value>
  </data>
  <data name="female" xml:space="preserve">
    <value>انثى</value>
  </data>
  <data name="founder_name" xml:space="preserve">
    <value>الاسم و اللقب</value>
  </data>
  <data name="g360_gallery_title" xml:space="preserve">
    <value>معرض °360</value>
  </data>
  <data name="gender" xml:space="preserve">
    <value>اختر جنسك</value>
  </data>
  <data name="get_offer" xml:space="preserve">
    <value>احصل على عرض</value>
  </data>
  <data name="get_offer_btn" xml:space="preserve">
    <value>احصل على عرض</value>
  </data>
  <data name="get_offer_text" xml:space="preserve">
    <value>اتخذ خطوتك وتواصل مع المختصصين في عالم الهندسة لاعطائكم افضل الاسعار المنافسة</value>
  </data>
  <data name="go_back" xml:space="preserve">
    <value>رجوع</value>
  </data>
  <data name="home" xml:space="preserve">
    <value>الصفحة الرئيسية</value>
  </data>
  <data name="home_our_projects" xml:space="preserve">
    <value>نبذة عنا</value>
  </data>
  <data name="home_ref_title" xml:space="preserve">
    <value />
  </data>
  <data name="home_slogan" xml:space="preserve">
    <value>نحن شركة متخصصة في المشاريع السكنية والتجارية الفاخرة والديكور والتصميم الداخلي والإكساء الخارجي . لقد قمنا بعمل مشاريع مذهلة في العديد من المحافظات التركية لتكون على قمة شركات الديكور في تركيا. تصاميمنا للديكور محترفة وشغوفة لتلبية طموح عملائنا.
                        &lt;br&gt;&lt;br&gt;
                        نستخدم كوادر تقنية و فنية بقدرات عالية و التي هي بدورها تضمن لكم الدقة في التصميم و الجودة في التنفيذ و الإشراف</value>
  </data>
  <data name="how_can_we_get" xml:space="preserve">
    <value>كيفية الرد</value>
  </data>
  <data name="how_email" xml:space="preserve">
    <value>البريد الإلكتروني</value>
  </data>
  <data name="how_phone" xml:space="preserve">
    <value>مكالمة هاتفية</value>
  </data>
  <data name="how_sms" xml:space="preserve">
    <value>رسالة قصيرة</value>
  </data>
  <data name="istanbul_vd" xml:space="preserve">
    <value>İstanbul.VD.</value>
  </data>
  <data name="istanbul_vd_number" xml:space="preserve">
    <value>7721236806</value>
  </data>
  <data name="job" xml:space="preserve">
    <value>اختر المهنة الخاصة بك</value>
  </data>
  <data name="logo_alt" xml:space="preserve">
    <value>SMD | SMD Decoration Architecture</value>
  </data>
  <data name="mail_and_fax" xml:space="preserve">
    <value>إتصل بنا</value>
  </data>
  <data name="male" xml:space="preserve">
    <value>ذكر</value>
  </data>
  <data name="message" xml:space="preserve">
    <value>الرسالة</value>
  </data>
  <data name="messages" xml:space="preserve">
    <value>الرسائل</value>
  </data>
  <data name="name" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="other_info" xml:space="preserve">
    <value>معلومات اخرى</value>
  </data>
  <data name="our_documents" xml:space="preserve">
    <value>وثائقنا</value>
  </data>
  <data name="our_founder" xml:space="preserve">
    <value>المدير التنفيذي</value>
  </data>
  <data name="our_founder_desc" xml:space="preserve">
    <value>تم تأسيس مجموعة اس ام دي الشركة الاولى و الرائدة في مجال تصميم الديكورات في تركيا smd decoration بإدارة المهندس أحمد مروان أنيس وقدم افضل الخدمات لعملائه في كل خطوة تقدم قام بها بعد تخرجه من جامعة حلب في سوريا عام 2012م و نحن كعائلة smd decoration نؤمن بإن نجاحنا و نجاحه عاد بلفائدة لجميع عملائنا</value>
  </data>
  <data name="our_founder_text" xml:space="preserve">
    <value>م.أحمد مروان أنيس</value>
  </data>
  <data name="our_projects" xml:space="preserve">
    <value>مشاريعنا</value>
  </data>
  <data name="our_projects_text" xml:space="preserve">
    <value>يمكنك العثور على بعض الأمثلة على أعمالنا &lt;span&gt; من خلال مشاهدة معرضنا. &lt;/span&gt;</value>
  </data>
  <data name="our_services" xml:space="preserve">
    <value>خدماتنا</value>
  </data>
  <data name="our_services_text" xml:space="preserve">
    <value>التصميم الثنائي والثلاثي الأبعاد والمخططات التنفيذية وحساب الكميات
و الإشراف و التنفيذ لجميع أنواع المشاريع الداخلية والخارجية
وصناعة جميع أنواع المجسمات الهندسية</value>
  </data>
  <data name="out_founder_text" xml:space="preserve">
    <value>هناك حقيقة مثبتة منذ زمن طويل وهي أن المحتوى المقروء لصفحة ما سيلهي القارئ عن التركيز على الشكل الخارجي للنص أو شكل توضع الفقرات في الصفحة التي يقرأها. ولذلك يتم استخدام طريقة لوريم إيبسوم لأنها تعطي توزيعاَ طبيعياَ -إلى حد ما- للأحرف عوضاً عن استخدام "هنا يوجد محتوى نصي، هنا يوجد محتوى نصي" فتجعلها تبدو (أي الأحرف) وكأنها نص مقروء. العديد من برامح النشر المكتبي وبرامح تحرير صفحات الويب تستخدم لوريم إيبسوم بشكل إفتراضي كنموذج عن النص، وإذا قمت بإدخالأي محرك</value>
  </data>
  <data name="photo_gallery" xml:space="preserve">
    <value>معرض الصور</value>
  </data>
  <data name="photo_gallery_title" xml:space="preserve">
    <value />
  </data>
  <data name="project_map_text" xml:space="preserve">
    <value>SMD GROUP تعمل من تركيا ولجميع دول العالم وفي جميع المناطق التركية SMD GROUP  في كل مكان</value>
  </data>
  <data name="project_map_title" xml:space="preserve">
    <value>يمكنك أن تجد بعض الأمثلة من أعمالنا</value>
  </data>
  <data name="project_see_more" xml:space="preserve">
    <value>انقر للحصول على تفاصيل المشروع</value>
  </data>
  <data name="project_video" xml:space="preserve">
    <value>فيديو المشروع</value>
  </data>
  <data name="projects_as_countries" xml:space="preserve">
    <value>المشاريع والبلدان</value>
  </data>
  <data name="projects_countries" xml:space="preserve">
    <value>المشاريع حسب البلدان</value>
  </data>
  <data name="projects_countries_text" xml:space="preserve">
    <value>SMD GROUP ليست فقط في تركيا وانما في جميع انحاء العالم</value>
  </data>
  <data name="see_all" xml:space="preserve">
    <value>اظهار الكل</value>
  </data>
  <data name="see_countries" xml:space="preserve">
    <value>شاهد المشاريع</value>
  </data>
  <data name="see_projects" xml:space="preserve">
    <value>القي نظرة حول المشاريع</value>
  </data>
  <data name="social_media" xml:space="preserve">
    <value>مواقع التواصل</value>
  </data>
  <data name="starting_date" xml:space="preserve">
    <value>تاريخ البدء</value>
  </data>
  <data name="submit_the_form" xml:space="preserve">
    <value>أرسل</value>
  </data>
  <data name="surname" xml:space="preserve">
    <value>اللقب</value>
  </data>
  <data name="tel" xml:space="preserve">
    <value>الموبايل</value>
  </data>
  <data name="telephone" xml:space="preserve">
    <value>الموبايل</value>
  </data>
  <data name="video_gallery" xml:space="preserve">
    <value>معرض الفيديو</value>
  </data>
  <data name="video_gallery_title" xml:space="preserve">
    <value>معرض الفيديو</value>
  </data>
  <data name="view_all" xml:space="preserve">
    <value>مشاهدة الكل</value>
  </data>
  <data name="view_awards" xml:space="preserve">
    <value>مشاهدة الجوائز</value>
  </data>
  <data name="view_details" xml:space="preserve">
    <value>عرض التفاصيل</value>
  </data>
  <data name="view_document" xml:space="preserve">
    <value>عرض المستند</value>
  </data>
  <data name="vision_mission" xml:space="preserve">
    <value>الرؤية و الاهداف</value>
  </data>
  <data name="vision_mission_text" xml:space="preserve">
    <value>رؤيتنا &lt;br&gt; في منزلك او مكتبك أو مكان عملك أو استثمارك نهدف لخلق الجمال والرقي و أينما تواجدت , يبدأ من فكرة المشروع العقاري مرورا بمراحل تنفيذها وحتي تسويق المشروع وبيعه إضافة الى التصميم الداخلي والديكور بالنسبة لنا ليس مجرد خدمة نقدمها ، بل .أسلوب حياة نخلق تصميمات تجعلك في قلب الحداثة. عملي ام جمالي إن التميز في التصميم ونجاحه في التنفيذ والجودة واألبحاث هما أمران أساسيان لنهجنا ، .كل مشروع هو فرصة فريدة لتثمين رؤية العميل وأن نتجاوز توقعاته &lt;br&gt; هدفنا &lt;br&gt; هدفنا وهدف عمالئنا واحد ونعمل جميعنا بيد واحدة لنحقق جميعنا الحلم المنشود في حدود الميزانيات المرصودة والجودة العالية والنجاح الباهر.</value>
  </data>
  <data name="we_always_wordk" xml:space="preserve">
    <value>&lt;span&gt;نسعى&lt;/span&gt; &lt;i class="fi flaticon-quality-1"&gt;&lt;/i&gt; دائما للافضل</value>
  </data>
  <data name="whyus" xml:space="preserve">
    <value>لماذا نحن</value>
  </data>
  <data name="whyus_text" xml:space="preserve">
    <value>&lt;p style="text-align:right;"&gt;نحن نعمل على تمثيل الشركات والمصانع التركية والاوروبية التي تعمل في مجالات :&lt;/p&gt;&lt;p style="text-align:right;"&gt;&amp;nbsp;&lt;/p&gt;&lt;p style="text-align:right;"&gt;- تجهيزات ومستلزمات الفنادق والمطاعم&lt;/p&gt;&lt;p style="text-align:right;"&gt;- كافة أنواع الحجر و الرخام الطبيعي والصناعي والسيراميك والباركيه&lt;/p&gt;&lt;p style="text-align:right;"&gt;- تامين كافة أنواع الابواب والنوافذ الخارجية و الداخلية المضادة للحرائق وغيرها واكسسواراتها&lt;/p&gt;&lt;p style="text-align:right;"&gt;- تجهيزات الصحية الحمامات و المطابخ واكسسواراتها&lt;/p&gt;&lt;p style="text-align:right;"&gt;- المفروشات المكتبية والمنزلية والمطابخ بانواعها&lt;/p&gt;&lt;p style="text-align:right;"&gt;- الاكسسوارت الكهربائية&lt;/p&gt;&lt;p style="text-align:right;"&gt;- تجهيزات الاسقف الكهربائية المتحركة&lt;/p&gt;</value>
  </data>
  <data name="year_winner" xml:space="preserve">
    <value>الفائز لهذا العام</value>
  </data>
  <data name="about_us" xml:space="preserve">
    <value>من نحن</value>
  </data>

  <data name="gallary" xml:space="preserve">
    <value>معرض</value>
  </data>
  <data name="ourblog" xml:space="preserve">
    <value>أخبارنا</value>
  </data>
  <data name="our_work" xml:space="preserve">
    <value>أعمالنا</value>
  </data>
  <data name="whysmd" xml:space="preserve">
    <value>لماذا مجموعة إس إم دي</value>
  </data>
  <data name="coproject" xml:space="preserve">
    <value>المشاريع التجارية</value>
  </data>
  <data name="hr" xml:space="preserve">
    <value>الموارد البشرية</value>
  </data>
  <data name="project" xml:space="preserve">
    <value>المشاريع</value>
  </data>
  <data name="resproject" xml:space="preserve">
    <value>المشاريع السكنية</value>
  </data>
  <data name="video" xml:space="preserve">
    <value>الفيديو</value>
  </data>
  <data name="from" xml:space="preserve">
    <value>من</value>
  </data>
  <data name="proj23" xml:space="preserve">
    <value>الصفحة</value>
    <comment>مشاريعنا</comment>
  </data>
  <data name="proj24" xml:space="preserve">
    <value>من</value>
    <comment>مشاريعنا</comment>
  </data>
  <data name="qslider" xml:space="preserve">
    <value>للاستفسار والتواصل :</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value>جولة 360 درجة</value>
  </data>
  <data name="String265" xml:space="preserve">
    <value>العودة الى القائمة</value>
  </data>
  <data name="String302" xml:space="preserve">
    <value>هل انت متأكد من عملية الحذف؟</value>
  </data>
  <data name="String308" xml:space="preserve">
    <value>حذف البند</value>
  </data>
  <data name="String317" xml:space="preserve">
    <value>تفاصيل البند</value>
  </data>
  <data name="String330" xml:space="preserve">
    <value>تاريخ الانشاء</value>
  </data>
  <data name="String331" xml:space="preserve">
    <value>تاريخ التعديل</value>
  </data>
  <data name="String10" xml:space="preserve">
    <value>تفاصيل</value>
  </data>
  <data name="String11" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="String12" xml:space="preserve">
    <value>اضافة بند</value>
  </data>
  <data name="String13" xml:space="preserve">
    <value>تعديل بند</value>
  </data>
  <data name="String2" xml:space="preserve">
    <value>اضافة</value>
  </data>
  <data name="String3" xml:space="preserve">
    <value>ادارة الجوائز</value>
  </data>
  <data name="String4" xml:space="preserve">
    <value>العنوان</value>
  </data>
  <data name="String5" xml:space="preserve">
    <value>السنة</value>
  </data>
  <data name="String6" xml:space="preserve">
    <value>اظهار في الصفحة الاولى</value>
  </data>
  <data name="String7" xml:space="preserve">
    <value>صورة</value>
  </data>
  <data name="String8" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="String9" xml:space="preserve">
    <value>تعديل</value>
  </data>
  <data name="String14" xml:space="preserve">
    <value>اللغة:</value>
  </data>
  <data name="String15" xml:space="preserve">
    <value>يرجى الاختيار</value>
  </data>
  <data name="String16" xml:space="preserve">
    <value>الرابط </value>
  </data>
  <data name="String17" xml:space="preserve">
    <value>رابط العربي</value>
  </data>
  <data name="String18" xml:space="preserve">
    <value>المحتوى</value>
  </data>
  <data name="String19" xml:space="preserve">
    <value>تحميل الصور</value>
  </data>
  <data name="String20" xml:space="preserve">
    <value>تحميل الملف</value>
  </data>
  <data name="String21" xml:space="preserve">
    <value>الصفحة</value>
  </data>
  <data name="String22" xml:space="preserve">
    <value>ادارة البلوكات</value>
  </data>
  <data name="String23" xml:space="preserve">
    <value>تعديل بلوك</value>
  </data>
  <data name="String31" xml:space="preserve">
    <value>اضافة جائزة</value>
  </data>
  <data name="String32" xml:space="preserve">
    <value>تعديل جائزة</value>
  </data>
  <data name="thanks" xml:space="preserve">
    <value>شكراً</value>
    <comment>شكرا</comment>
  </data>
  <data name="thanks1" xml:space="preserve">
    <value>لقد تم استلام طلبكم وسيتم الاتصال بكم للمتابعة</value>
    <comment>شكرا</comment>
  </data>
  <data name="String24" xml:space="preserve">
    <value>الحقل مطلوب</value>
  </data>
  <data name="String25" xml:space="preserve">
    <value>يرجى كتابة الايميل بالشكل الصحيح</value>
  </data>
  <data name="String26" xml:space="preserve">
    <value>يرجى ادخال رقم الهاتف بشكل صحيح</value>
  </data>
  <data name="String27" xml:space="preserve">
    <value>اختر مهنتك</value>
  </data>
  <data name="String28" xml:space="preserve">
    <value>اختر المهنة الخاصة بك</value>
  </data>
  <data name="String29" xml:space="preserve">
    <value>محرر فيديوهات</value>
  </data>
  <data name="String30" xml:space="preserve">
    <value>محاسب</value>
  </data>
  <data name="String33" xml:space="preserve">
    <value>كاتب محتوى</value>
  </data>
  <data name="String34" xml:space="preserve">
    <value>مصمم</value>
  </data>
  <data name="String35" xml:space="preserve">
    <value>مبرمج</value>
  </data>
  <data name="String36" xml:space="preserve">
    <value>مصمم جرافيك</value>
  </data>
  <data name="String37" xml:space="preserve">
    <value>مهندس معماري</value>
  </data>
  <data name="String38" xml:space="preserve">
    <value>منسق</value>
  </data>
  <data name="String39" xml:space="preserve">
    <value>مدير مشاريع</value>
  </data>
  <data name="String40" xml:space="preserve">
    <value>العنوان</value>
  </data>
  <data name="String41" xml:space="preserve">
    <value>معلومات اخرى</value>
  </data>
  <data name="String42" xml:space="preserve">
    <value>السلايدر</value>
  </data>
  <data name="String43" xml:space="preserve">
    <value>الاخبار الاكثر زيارة</value>
  </data>
  <data name="String44" xml:space="preserve">
    <value>المشاريع الاكثر زيارة</value>
  </data>
  <data name="String45" xml:space="preserve">
    <value>لوحة التحكم</value>
  </data>
  <data name="String46" xml:space="preserve">
    <value>الموقع</value>
  </data>
  <data name="String47" xml:space="preserve">
    <value>فئات المشاريع</value>
  </data>
  <data name="String48" xml:space="preserve">
    <value>فئات الاخبار</value>
  </data>
  <data name="String49" xml:space="preserve">
    <value>SEO Page</value>
  </data>
  <data name="String50" xml:space="preserve">
    <value>اضافة مدونة</value>
  </data>
  <data name="String51" xml:space="preserve">
    <value>تعديل مدونة</value>
  </data>
  <data name="String52" xml:space="preserve">
    <value>اضافة مشروع</value>
  </data>
  <data name="String53" xml:space="preserve">
    <value>تعديل مشروع</value>
  </data>
  <data name="String54" xml:space="preserve">
    <value>منوع</value>
  </data>
  <data name="String55" xml:space="preserve">
    <value>اضافة 360 درجة</value>
  </data>
  <data name="String56" xml:space="preserve">
    <value>تعديل 360 درجة</value>
  </data>
  <data name="String57" xml:space="preserve">
    <value>اضافة فيديو </value>
  </data>
  <data name="String58" xml:space="preserve">
    <value>تعديل فيديو</value>
  </data>
  <data name="String59" xml:space="preserve">
    <value>اضافة موظف</value>
  </data>
  <data name="String60" xml:space="preserve">
    <value>تعديل موظف</value>
  </data>
  <data name="String61" xml:space="preserve">
    <value>اسم المستخدم</value>
  </data>
  <data name="String62" xml:space="preserve">
    <value>كلمة المرور</value>
  </data>
  <data name="String63" xml:space="preserve">
    <value>الصلاحية</value>
  </data>
  <data name="String64" xml:space="preserve">
    <value>فعال</value>
  </data>
  <data name="String65" xml:space="preserve">
    <value>360 درجة</value>
  </data>
  <data name="String66" xml:space="preserve">
    <value>صورة الغلاف</value>
  </data>
  <data name="String67" xml:space="preserve">
    <value>smd videos</value>
  </data>
  <data name="String68" xml:space="preserve">
    <value>فئات الفيديو</value>
  </data>
  <data name="String69" xml:space="preserve">
    <value>تحميل</value>
  </data>
  <data name="String70" xml:space="preserve">
    <value>ادارة الصفحات</value>
  </data>
  <data name="String71" xml:space="preserve">
    <value>تعديل الصفحة</value>
  </data>
  <data name="String72" xml:space="preserve">
    <value>عدد الزيارات</value>
  </data>
  <data name="String73" xml:space="preserve">
    <value>ادارة فئات الاخبار</value>
  </data>
  <data name="String74" xml:space="preserve">
    <value>اضافة فئة خبر</value>
  </data>
  <data name="String75" xml:space="preserve">
    <value>تعديل فئة خبر</value>
  </data>
  <data name="String76" xml:space="preserve">
    <value>تفاصيل فئة خبر</value>
  </data>
  <data name="String77" xml:space="preserve">
    <value>حذف فئة خبر</value>
  </data>
  <data name="String78" xml:space="preserve">
    <value>ادارة فئات المشاريع</value>
  </data>
  <data name="String79" xml:space="preserve">
    <value>اضافة فئة مشروع</value>
  </data>
  <data name="String80" xml:space="preserve">
    <value>تعديل فئة مشروع</value>
  </data>
  <data name="String81" xml:space="preserve">
    <value>تفاصيل فئة مشروع </value>
  </data>
  <data name="String82" xml:space="preserve">
    <value>حذف فئة مشروع</value>
  </data>
  <data name="String83" xml:space="preserve">
    <value>الترتيب</value>
  </data>
  <data name="String84" xml:space="preserve">
    <value>ادارة 360 درجة</value>
  </data>
  <data name="String85" xml:space="preserve">
    <value>ادارة الفيديوهات</value>
  </data>
  <data name="String86" xml:space="preserve">
    <value>فئة الاخبار</value>
  </data>
  <data name="String87" xml:space="preserve">
    <value>فئة المشروع</value>
  </data>
  <data name="String88" xml:space="preserve">
    <value>نشر</value>
  </data>
  <data name="String89" xml:space="preserve">
    <value>اظهار وتساب</value>
  </data>
  <data name="String90" xml:space="preserve">
    <value>اظهار مربع التعليقات</value>
  </data>
  <data name="String91" xml:space="preserve">
    <value>المؤلف</value>
  </data>
  <data name="String92" xml:space="preserve">
    <value>خط الطول</value>
  </data>
  <data name="String93" xml:space="preserve">
    <value>خط العرض</value>
  </data>
  <data name="String94" xml:space="preserve">
    <value>سلايدر مشروع</value>
  </data>
  <data name="String95" xml:space="preserve">
    <value>انشاء سلايدر مشروع</value>
  </data>
  <data name="String96" xml:space="preserve">
    <value>الخبر</value>
  </data>
  <data name="String100" xml:space="preserve">
    <value>تفاصيل عميل</value>
  </data>
  <data name="String101" xml:space="preserve">
    <value>حذف عميل</value>
  </data>
  <data name="String102" xml:space="preserve">
    <value>ادارة السلايدر</value>
  </data>
  <data name="String103" xml:space="preserve">
    <value>اضافة سلايدر</value>
  </data>
  <data name="String104" xml:space="preserve">
    <value>تعديل سلايدر</value>
  </data>
  <data name="String105" xml:space="preserve">
    <value>تفاصيل سلايدر</value>
  </data>
  <data name="String106" xml:space="preserve">
    <value>حذف سلايدر</value>
  </data>
  <data name="String107" xml:space="preserve">
    <value>ادارة فئات الفيديو</value>
  </data>
  <data name="String108" xml:space="preserve">
    <value>اضافة فئة فيديو</value>
  </data>
  <data name="String109" xml:space="preserve">
    <value>تعديل فئة فيديو</value>
  </data>
  <data name="String110" xml:space="preserve">
    <value>تفاصيل فئة فيديو</value>
  </data>
  <data name="String111" xml:space="preserve">
    <value>حذف فئة فيديو</value>
  </data>
  <data name="String112" xml:space="preserve">
    <value>ادارة فيديوهات اس ام دي</value>
  </data>
  <data name="String113" xml:space="preserve">
    <value>اضافة فيديو</value>
  </data>
  <data name="String114" xml:space="preserve">
    <value>تعديل فيديو</value>
  </data>
  <data name="String115" xml:space="preserve">
    <value>تفاصيل فيديو </value>
  </data>
  <data name="String97" xml:space="preserve">
    <value>ادارة العملاء</value>
  </data>
  <data name="String98" xml:space="preserve">
    <value>اضافة عميل</value>
  </data>
  <data name="String99" xml:space="preserve">
    <value>تعديل عميل</value>
  </data>
  <data name="String116" xml:space="preserve">
    <value>حذف فيديو</value>
  </data>
  <data name="String117" xml:space="preserve">
    <value>سؤال وجواب</value>
  </data>
  <data name="String118" xml:space="preserve">
    <value>ادارة سؤال وجواب</value>
  </data>
  <data name="String119" xml:space="preserve">
    <value>اضافة سؤال وجواب</value>
  </data>
  <data name="String120" xml:space="preserve">
    <value>تعديل سؤال وجواب</value>
  </data>
  <data name="String121" xml:space="preserve">
    <value>تفاصيل سؤال وجواب</value>
  </data>
  <data name="String122" xml:space="preserve">
    <value>حذف سؤال وجواب</value>
  </data>
  <data name="String123" xml:space="preserve">
    <value>السؤال</value>
  </data>
  <data name="String124" xml:space="preserve">
    <value>الجواب</value>
  </data>
  <data name="String125" xml:space="preserve">
    <value>مركز الصور </value>
  </data>
  <data name="String126" xml:space="preserve">
    <value>اضافة مجموعة صور</value>
  </data>
  <data name="String127" xml:space="preserve">
    <value>نسخ الرابط</value>
  </data>
  <data name="category" xml:space="preserve">
    <value>اهم الاقسام في الديكور</value>
  </data>
  <data name="important" xml:space="preserve">
    <value>روابط مهمة</value>
  </data>
  <data name="jobs" xml:space="preserve">
    <value>فرص عمل</value>
  </data>
  <data name="nslogan" xml:space="preserve">
    <value>SMD Decoration اهم &lt;span style=" color: #bf9d7f;"&gt;شركة ديكور في اسطنبول&lt;/span&gt; اضافة لاعمالها في السعودية والامارات وقطر وليبيا والعراق</value>
  </data>
  <data name="cv" xml:space="preserve">
    <value />
  </data>
  <data name="our_foun_text" xml:space="preserve">
    <value />
  </data>
  <data name="file" xml:space="preserve">
    <value />
  </data>
  <data name="saudi" xml:space="preserve">
    <value>نحن في السعودية</value>
  </data>
  <data name="suaditext" xml:space="preserve">
    <value>إذا كنت تبحث عن أفكار مبتكرة في الديكور والتصميم الداخلي، فقد وصلت إلى المكان الصحيح. نحن نقدم حلولاً فريدة تدمج بين الأناقة والراحة لتصميم المساحات التي تلبي احتياجاتك وتتناسب مع ذوقك الشخصي. سواء كنت تبحث عن تجديد منزلك أو تصميم مكتبك بأحدث أساليب الديكور العصري، نحن هنا لنقدم لك أفضل الاستشارات والخدمات. تابع مشاريعنا الجديدة الآن في المملكة العربية السعودية حيث نستمر بتوسيع أعمالنا وتقديم خبراتنا في فروعنا الجديدة. لا تفوت الفرصة، قم بتسجيل الدخول الآن لمتابعة كل جديد!</value>
  </data>
  <data name="String128" xml:space="preserve">
    <value>موقع العروض</value>
  </data>
  <data name="String129" xml:space="preserve">
    <value>الموقع السعودي الرسمي لشركتنا</value>
  </data>
  <data name="AIGeneratorTitle" xml:space="preserve">
    <value>مولد المخططات المعمارية الذكي</value>
  </data>
  <data name="AIGeneratorSubtitle" xml:space="preserve">
    <value>قم بإنشاء مخططات معمارية احترافية باستخدام الذكاء الاصطناعي</value>
  </data>

  <data name="WelcomeMessageBody" xml:space="preserve">
    <value>ابدأ رحلتك الإبداعية في تصميم مخططك المعماري المميز. كل ما تحتاجه هو خيالك، ونحن سنحول أفكارك إلى واقع!</value>
  </data>
  <data name="AvailableDrawingsTitle" xml:space="preserve">
    <value>أنواع المخططات المتاحة</value>
  </data>
  <data name="Copyright" xml:space="preserve">
        <value>&#169; 2024 SMD Decoration Architecture. جميع الحقوق محفوظة.</value>
  </data>
  <data name="DrawingCardSitePlanTitle" xml:space="preserve">
    <value>مخطط الموقع العام</value>
  </data>
  <data name="DrawingCardSitePlanDescription" xml:space="preserve">
    <value>يُظهر موقع البناء على الأرض بالنسبة للشارع والمباني المجاورة</value>
  </data>
  <data name="DrawingCardFloorPlanTitle" xml:space="preserve">
    <value>مخطط المسقط الأفقي</value>
  </data>
  <data name="DrawingCardFloorPlanDescription" xml:space="preserve">
    <value>يُظهر توزيع الغرف والجدران والأبواب والنوافذ لكل طابق</value>
  </data>
  <data name="DrawingCardElevationsTitle" xml:space="preserve">
    <value>مخطط الواجهات</value>
  </data>
  <data name="DrawingCardElevationsDescription" xml:space="preserve">
    <value>يُظهر شكل الواجهات الخارجية للمبنى</value>
  </data>
  <data name="DrawingCardSectionsTitle" xml:space="preserve">
    <value>مخطط المقاطع الرأسية</value>
  </data>
  <data name="DrawingCardSectionsDescription" xml:space="preserve">
    <value>يُوضح القطاعات الرأسية للبناء</value>
  </data>
  <data name="DrawingCardDetailsTitle" xml:space="preserve">
    <value>مخطط التفاصيل</value>
  </data>
  <data name="DrawingCardDetailsDescription" xml:space="preserve">
    <value>يُستخدم لإظهار تفاصيل دقيقة كالأبواب والنوافذ والدرج</value>
  </data>
  <data name="DrawingCardRoofingTitle" xml:space="preserve">
    <value>مخطط التسقيف</value>
  </data>
  <data name="DrawingCardRoofingDescription" xml:space="preserve">
    <value>يُبين تصميم السقف والهيكل العلوي</value>
  </data>
  <data name="DrawingCardFurnitureTitle" xml:space="preserve">
    <value>مخططات التأثيث</value>
  </data>
  <data name="DrawingCardFurnitureDescription" xml:space="preserve">
    <value>توضح توزيع الأثاث داخل المساحات</value>
  </data>
  <data name="FormStartBuildingTitle" xml:space="preserve">
    <value>ابدأ بإنشاء مخططاتك المعمارية</value>
  </data>
  <data name="FormProjectNameLabel" xml:space="preserve">
    <value>اسم المشروع</value>
  </data>
  <data name="FormBuildingTypeLabel" xml:space="preserve">
    <value>نوع المبنى</value>
  </data>
  <data name="FormBuildingTypePlaceholder" xml:space="preserve">
    <value>اختر نوع المبنى</value>
  </data>
  <data name="FormBuildingTypeResidential" xml:space="preserve">
    <value>سكني</value>
  </data>
  <data name="FormBuildingTypeCommercial" xml:space="preserve">
    <value>تجاري</value>
  </data>
  <data name="FormBuildingTypeOffice" xml:space="preserve">
    <value>مكتبي</value>
  </data>
  <data name="FormBuildingTypeVilla" xml:space="preserve">
    <value>فيلا</value>
  </data>
  <data name="FormBuildingTypeApartment" xml:space="preserve">
    <value>شقة</value>
  </data>
  <data name="FormBuildingTypeWarehouse" xml:space="preserve">
    <value>مستودع</value>
  </data>
  <data name="FormPlotAreaLabel" xml:space="preserve">
    <value>مساحة الأرض (متر مربع)</value>
  </data>
  <data name="FormFloorsLabel" xml:space="preserve">
    <value>عدد الطوابق</value>
  </data>
  <data name="FormFloorsPlaceholder" xml:space="preserve">
    <value>اختر عدد الطوابق</value>
  </data>
  <data name="FormFloorsOne" xml:space="preserve">
    <value>طابق واحد</value>
  </data>
  <data name="FormFloorsTwo" xml:space="preserve">
    <value>طابقين</value>
  </data>
  <data name="FormFloorsThree" xml:space="preserve">
    <value>ثلاثة طوابق</value>
  </data>
  <data name="FormFloorsFour" xml:space="preserve">
    <value>أربعة طوابق</value>
  </data>
  <data name="FormFloorsFivePlus" xml:space="preserve">
    <value>أكثر من 4 طوابق</value>
  </data>
  <data name="FormRoomsLabel" xml:space="preserve">
    <value>عدد الغرف</value>
  </data>
  <data name="FormArchitecturalStyleLabel" xml:space="preserve">
    <value>الطراز المعماري</value>
  </data>
  <data name="FormArchitecturalStylePlaceholder" xml:space="preserve">
    <value>اختر الطراز</value>
  </data>
  <data name="FormArchitecturalStyleModern" xml:space="preserve">
    <value>حديث</value>
  </data>
  <data name="FormArchitecturalStyleClassic" xml:space="preserve">
    <value>كلاسيكي</value>
  </data>
  <data name="FormArchitecturalStyleIslamic" xml:space="preserve">
    <value>إسلامي</value>
  </data>
  <data name="FormArchitecturalStyleContemporary" xml:space="preserve">
    <value>معاصر</value>
  </data>
  <data name="FormArchitecturalStyleMinimalist" xml:space="preserve">
    <value>بسيط</value>
  </data>
  <data name="ArchitecturalStyleNeoclassical" xml:space="preserve">
    <value>كلاسيكي جديد</value>
  </data>
  <data name="BudgetLabel" xml:space="preserve">
    <value>الميزانية التقديرية</value>
  </data>
  <data name="BudgetPlaceholder" xml:space="preserve">
    <value>اختر الميزانية</value>
  </data>
  <data name="BudgetLow" xml:space="preserve">
    <value>أقل من 100,000 ريال</value>
  </data>
  <data name="BudgetMedium" xml:space="preserve">
    <value>100,000 - 500,000 ريال</value>
  </data>
  <data name="BudgetHigh" xml:space="preserve">
    <value>500,000 - 1,000,000 ريال</value>
  </data>
  <data name="BudgetPremium" xml:space="preserve">
    <value>أكثر من 1,000,000 ريال</value>
  </data>
  <data name="SpecialRequirementsLabel" xml:space="preserve">
    <value>متطلبات خاصة</value>
  </data>
  <data name="SpecialRequirementsPlaceholder" xml:space="preserve">
    <value>أدخل أي متطلبات خاصة أو ملاحظات إضافية...</value>
  </data>
  <data name="GenerateButtonText" xml:space="preserve">
    <value>إنشاء المخططات المعمارية</value>
  </data>
  <data name="GeneratingButtonText" xml:space="preserve">
    <value>جاري إنشاء المخططات...</value>
  </data>

  <data name="SystemUser" xml:space="preserve">
    <value>مستخدم النظام</value>
  </data>
  <data name="AvailableCredit" xml:space="preserve">
    <value>الرصيد المتاح</value>
  </data>
  <data name="Points" xml:space="preserve">
    <value>150 نقطة</value>
  </data>
  <data name="UserHighlightMessage" xml:space="preserve">
    <value>استخدم الذكاء الاصطناعي لإنشاء تصاميم معمارية احترافية في دقائق معدودة</value>
  </data>
  <data name="BadgeAdvanced" xml:space="preserve">
    <value>متقدم</value>
  </data>
  <data name="Points25" xml:space="preserve">
    <value>25 نقطة</value>
  </data>
  <data name="BadgeBasic" xml:space="preserve">
    <value>أساسي</value>
  </data>
  <data name="Points15" xml:space="preserve">
    <value>15 نقطة</value>
  </data>
  <data name="BadgeTechnical" xml:space="preserve">
    <value>تقني</value>
  </data>
  <data name="Points20" xml:space="preserve">
    <value>20 نقطة</value>
  </data>
  <data name="BadgeDetailed" xml:space="preserve">
    <value>تفصيلي</value>
  </data>
  <data name="Points30" xml:space="preserve">
    <value>30 نقطة</value>
  </data>
  <data name="BadgeAccurate" xml:space="preserve">
    <value>دقيق</value>
  </data>
  <data name="Points35" xml:space="preserve">
    <value>35 نقطة</value>
  </data>
  <data name="BadgeStructural" xml:space="preserve">
    <value>هيكلي</value>
  </data>
  <data name="Points18" xml:space="preserve">
    <value>18 نقطة</value>
  </data>
  <data name="BadgeDesign" xml:space="preserve">
    <value>تصميمي</value>
  </data>
  <data name="Points22" xml:space="preserve">
    <value>22 نقطة</value>
  </data>

  <data name="SquareMeters" xml:space="preserve">
    <value>م²</value>
  </data>
  <data name="FormArchitecturalStyleTraditional" xml:space="preserve">
    <value>تقليدي</value>
  </data>
  <data name="GenerationTimeMessage" xml:space="preserve">
    <value>سيتم إنشاء التصاميم خلال دقائق معدودة</value>
  </data>
  <data name="SufficientCreditMessage" xml:space="preserve">
    <value>تأكد من وجود رصيد كافٍ قبل البدء في عملية التوليد</value>
  </data>
  <data name="Help" xml:space="preserve">
    <value>المساعدة</value>
  </data>
  <data name="Support" xml:space="preserve">
    <value>الدعم</value>
  </data>

  <data name="PageTitle" xml:space="preserve">
    <value>مولد التصاميم المعمارية بالذكاء الاصطناعي</value>
  </data>
  <data name="CostEstimation" xml:space="preserve">
    <value>تقدير التكلفة</value>
  </data>
  <data name="CostEstimationDescription" xml:space="preserve">
    <value>سيتم حساب التكلفة بناءً على نوع التصاميم المطلوبة</value>
  </data>
  <data name="Points45" xml:space="preserve">
    <value>~ 45 نقطة</value>
  </data>
  <data name="ExpectedCost" xml:space="preserve">
    <value>تكلفة متوقعة</value>
  </data>
</root>