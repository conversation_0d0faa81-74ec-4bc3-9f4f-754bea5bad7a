﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using System.Web.Http.Cors;

namespace smd
{
    public static class WebApiConfig
    {
        public static void Register(HttpConfiguration config)
        {
            config.Formatters.JsonFormatter.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;

            var corsAttr = new EnableCorsAttribute("https://smddecoration.qa,https://smddecoration.ae,https://smddecoration.net,https://smddecoration.info,https://turkiyedecoration.site,https://smddecoration.net.tr,https://smddecoration.netlify.app,https://smddecorations.web.app", "*", "*");
            config.EnableCors(corsAttr);

            // Web API routes
            config.MapHttpAttributeRoutes();

            config.Routes.MapHttpRoute(
                name: "Default<PERSON><PERSON>",
                routeTemplate: "api/{controller}/{id}",
                defaults: new { id = RouteParameter.Optional }
            );
        }
    }
}
