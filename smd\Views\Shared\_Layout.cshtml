﻿<!DOCTYPE html>
@{
    string currentLang = Session["CurrentLanguage"] as string;
    string lang = "ar";
    string hreflangCode = "ar";
    string canonicalUrl = ViewBag.OGUrlcanon;

    if (currentLang == "en-En")
    {
        lang = "en";
        hreflangCode = "en";
    }
    else if (currentLang == "tr-tr")
    {
        lang = "tr";
        hreflangCode = "tr";
    }
}
<html lang="@lang">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- SEO Meta Tags -->
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="bingbot" content="index, follow">

    @if (!string.IsNullOrEmpty(ViewBag.OGDesc))
    {
        <meta name="description" content="@ViewBag.OGDesc">
    }
    else
    {
        <meta name="description" content="SMD Decoration - شركة رائدة في مجال الديكور والتصميم الداخلي في تركيا. نقدم أحدث الديكورات والدهانات والتصاميم العصرية للمنازل والمكاتب.">
    }

    @if (!string.IsNullOrEmpty(ViewBag.keyword))
    {
        <meta name="keywords" content="@ViewBag.keyword">
    }
    else
    {
        <meta name="keywords" content="ديكور تركيا, تصميم داخلي, دهانات, ديكورات منازل, شركة ديكور, SMD Decoration, interior design Turkey">
    }

    <!-- Author and Publisher -->
    <meta name="author" content="SMD Decoration">
    <meta name="publisher" content="SMD Decoration">
    <meta name="copyright" content="SMD Decoration">

    <!-- Additional SEO Meta Tags -->
    <meta name="audience" content="all">
    <meta name="rating" content="General">
    <meta name="revisit-after" content="1 days">
    <meta name="distribution" content="Global">
    <meta name="language" content="@lang">
    <meta name="geo.region" content="TR">
    <meta name="geo.country" content="Turkey">

    @RenderSection("MetaTags", required: false)
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@(ViewBag.OGTitle ?? "SMD Decoration - ديكورات تركيا")" />
    <meta property="og:description" content="@(ViewBag.OGDesc ?? "شركة رائدة في مجال الديكور والتصميم الداخلي في تركيا")" />
    <meta property="og:url" content="@ViewBag.OGUrl">
    <meta property="og:image" content="@(ViewBag.OGImage ?? "https://smddecoration.com/Content/images/smd-logo-og.jpg")" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:type" content="image/jpeg" />
    <meta property="og:image:alt" content="@(ViewBag.OGTitle ?? "SMD Decoration Logo")" />
    <meta property="og:type" content="website" />
    <meta property="og:locale" content="@(lang == "ar" ? "ar_SA" : lang == "en" ? "en_US" : "tr_TR")" />
    <meta property="og:site_name" content="SMD Decoration" />
    <meta property="fb:app_id" content="1249232692356920" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@("@")smddecoration">
    <meta name="twitter:creator" content="@("@")smddecoration">
    <meta name="twitter:title" content="@(ViewBag.OGTitle ?? "SMD Decoration - ديكورات تركيا")">
    <meta name="twitter:description" content="@(ViewBag.OGDesc ?? "شركة رائدة في مجال الديكور والتصميم الداخلي في تركيا")">
    <meta name="twitter:image" content="@(ViewBag.OGImage ?? "https://smddecoration.com/Content/images/smd-logo-twitter.jpg")">
    <meta name="twitter:image:alt" content="@(ViewBag.OGTitle ?? "SMD Decoration Logo")"

    <!-- Additional Schema.org markup -->
    <meta itemprop="name" content="@(ViewBag.OGTitle ?? "SMD Decoration")">
    <meta itemprop="description" content="@(ViewBag.OGDesc ?? "شركة رائدة في مجال الديكور والتصميم الداخلي في تركيا")">
    <meta itemprop="image" content="@(ViewBag.OGImage ?? "https://smddecoration.com/Content/images/smd-logo.jpg")">

    @if (ViewBag.st == "1")
    {
        <meta http-equiv="refresh" content="0; url=@ViewBag.url" />
        <script>window.location.href = "@ViewBag.url";</script>
    }

    <meta name="twitter:card" content="summary">
    <meta property="twitter:title" content="@ViewBag.OGTitle" />
    <meta property="twitter:description" content="@ViewBag.OGDesc" />
    <meta property="twitter:url" content="@ViewBag.OGUrl">
    <meta property="twitter:image" content="@ViewBag.OGImage" />
    <meta name="twitter:site" content="@("@smddecoration")">
    <meta name="twitter:creator" content="@ViewBag.OGTitle">

    <meta http-equiv="content-language" content="@lang">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1,user-scalable=no">
    <meta name="p:domain_verify" content="75a9b3aff5d936d02db55906b1c72018" />

    <!--[if lt IE 9]>
        <script src="http://css3-mediaqueries-js.googlecode.com/svn/trunk/css3-mediaqueries.js"></script>
    <![endif]-->
    <title>@ViewBag.Title</title>
    <link rel="icon" href="/assets/img/favicon.png" type="image/png">
    
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.1/jquery.min.js"></script>
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/jquery.lazyloadxt/1.1.0/jquery.lazyloadxt.min.js" integrity="sha512-3vivnUcccTdHsFyh6mAPtdxQ09Ihk1PbBajJ0PSshJftB7ekRxDmMsM4aKS0Ja1bfgkUgde71N2k1sDzQ9NvTg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/jquery.lazyloadxt/1.1.0/jquery.lazyloadxt.bg.min.js" integrity="sha512-I0tLLaeP0qfkO5U9Pchv03/jJ1sxJjGudAbNlEUP1yQfV8sLkeFd+6qekvWRAM0uyIwcOzQaSqwLRfp89beJFQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link defer href="/assets/css/style.css?v=1.06" rel="stylesheet">

    <script Async type="text/javascript" src="/assets/js/maplace.js"></script>


    <script>
        $('html').attr('dir', '@(lang == "ar" ? "rtl" : "ltr")');
    </script>

    @if (!string.IsNullOrEmpty(canonicalUrl))
    {
        <link rel="canonical" href="@canonicalUrl">
    }
    
    @* Hreflang implementation *@
    @if (!string.IsNullOrEmpty(ViewBag.OGUrlcar))
    {
        <link rel="alternate" href="@ViewBag.OGUrlcar" hreflang="ar" />
    }
    @if (!string.IsNullOrEmpty(ViewBag.OGUrlcen))
    {
        <link rel="alternate" href="@ViewBag.OGUrlcen" hreflang="en" />
    }
    @if (!string.IsNullOrEmpty(ViewBag.OGUrlctr))
    {
        <link rel="alternate" href="@ViewBag.OGUrlctr" hreflang="tr" />
    }
    
    @* Default hreflang for x-default *@
    @if (!string.IsNullOrEmpty(ViewBag.OGUrlcar))
    {
        <link rel="alternate" href="@ViewBag.OGUrlcar" hreflang="x-default" />
    }

    <style>
        .svg-div {
            text-align: center;
        }

        rect, circle, path {
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: draw 10s linear forwards;
        }

        @@keyframes draw {
            from {
                stroke-dashoffset: 1000;
            }

            to {
                stroke-dashoffset: 0;
            }
        }
    </style>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-TYFT6HNX03"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-TYFT6HNX03');
    </script>
    <!-- Google Tag Manager -->
    <script>
        (function (w, d, s, l, i) {
            w[l] = w[l] || []; w[l].push({
                'gtm.start':
                    new Date().getTime(), event: 'gtm.js'
            }); var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-MFHGGKT');</script>
    <!-- End Google Tag Manager -->
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@@context": "https://schema.org",
        "@@type": "Organization",
        "name": "SMD Decoration",
        "url": "https://smddecoration.com",
        "logo": "https://smddecoration.com/assets/img/logo.svg",
        "description": "@(ViewBag.OGDesc ?? "SMD Decoration - Professional Interior Design and Decoration Services")",
        "address": {
            "@@type": "PostalAddress",
            "streetAddress": "Beylizdüzü - Yakuplu Mah. 59. Sk. No:40 Aston A blok Kat:3 D:37",
            "addressLocality": "Istanbul",
            "addressCountry": "TR"
        },
        "contactPoint": {
            "@@type": "ContactPoint",
            "telephone": "+90-553-904-12-12",
            "contactType": "customer service",
            "email": "<EMAIL>"
        },
        "sameAs": [
            "https://www.facebook.com/smddecoration",
            "https://www.instagram.com/smddecoration/",
            "https://twitter.com/smddecoration",
            "https://www.youtube.com/channel/UClLgEoAFdToI0v4VBT548OA",
            "https://www.linkedin.com/company/smddecoration/"
        ]
    }
    </script>
    
    @RenderSection("StructuredData", required: false)
</head>

<body>
    <!-- Google Tag Manager (noscript) -->
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MFHGGKT"
                height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>
    <!-- End Google Tag Manager (noscript) -->
    @using smd.service;
    @{
        smdservices t = new smdservices();


        string value1 = t.vidcattitle(1, ViewBag.lang);


    }
    <span id="one7" onclick="openNav1()" style="-webkit-box-shadow: 6px 7px 7px -3px rgba(0,0,0,0.82);
-moz-box-shadow: 6px 7px 7px -3px rgba(0,0,0,0.82);
box-shadow: 6px 7px 7px -3px rgba(0,0,0,0.82); background-color:white;position:fixed;z-index:9999999;top:60%;left:0 ; vertical-align:middle;padding:5px; width:70px; height:auto;border-radius:0 5px 5px 0;cursor:pointer">
        <div class="svg-div">

            <svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 172.5 152">
                <defs>
                    <style>
                        .cls-1, .cls-2 {
                            fill: none;
                            stroke: #bf9d7f;
                            stroke-miterlimit: 10;
                        }

                        .cls-1 {
                            stroke-width: 8px;
                        }

                        .cls-2 {
                            stroke-width: 7px;
                        }
                    </style>
                </defs>

                <rect class="cls-1" x="14" y="73" width="100" height="75" rx="13.37" ry="13.37" />
                <rect class="cls-2" x="122" y="90" width="16" height="39" rx="3.86" ry="3.86" />
                <path class="cls-2" d="M239.5,213.5c-1.95-2.05-4.13-3.88-6-6a106.6,106.6,0,0,0-9-9,13.19,13.19,0,0,1-1.69-2.31A13.51,13.51,0,0,1,221.5,193V164a13.6,13.6,0,0,1,1.31-3.15,13.32,13.32,0,0,1,2.69-3.31l14-11a2.31,2.31,0,0,1,3-1,2.58,2.58,0,0,1,1,2c.47,16,.28,23.95,1,66,0,.71-.08,2.66-1,3S241.31,215.41,239.5,213.5Z" transform="translate(-75.5 -69.5)" />
                <circle class="cls-1" cx="30" cy="38" r="26" />
                <circle class="cls-1" cx="94.5" cy="33.5" r="29.5" />
            </svg>
            <p>@value1</p>

        </div>
    </span>

    <header>
        <div class="top">
            <div>
                <div class="contact">
                    <a href="tel:+90 553 904 12 12"><i class="fi flaticon-telephone-call"></i> +90 553 904 12 12</a>
                    <a href="mailto:<EMAIL>"><i class="fi flaticon-email"></i> <EMAIL></a>
                </div>


                <div class="social">
                    <a target="_blank" href="https://www.instagram.com/smddecoration/"><i class="fi flaticon-instagram"></i> Instagram</a>
                    <a target="_blank" href="https://twitter.com/smddecoration"><i class="fi flaticon-twitter"></i> Twitter</a>
                    <a target="_blank" href="https://www.facebook.com/smddecoration"><i class="fi flaticon-facebook-logo"></i> Facebook</a>
                    <a target="_blank" href="https://www.youtube.com/channel/UClLgEoAFdToI0v4VBT548OA?view_as=subscriber"><i class="fab fa-youtube"></i>Youtube</a>
                    <a target="_blank" href="https://www.tiktok.com/@('@')smddecoration"><i class="fab fa-tiktok"></i>Tiktok</a>
                    <a target="_blank" href="https://tr.pinterest.com/smddecoration/"><i class="fa-brands fa-pinterest-p"></i>Pinterest</a>
                    <a target="_blank" href="https://www.behance.net/smddecoration/"><i class="fab fa-behance"></i>Behance</a>
                    <a target="_blank" href="https://www.linkedin.com/company/smddecoration/"><i class="fab fa-linkedin"></i>Linkedin</a>
                </div>

            </div>
        </div>
        <div class="bottom">
            <a href="@Url.Action("Index", "Home")">
                <img src="/assets/img/logo.svg" alt="SMD | SMD Decoration Architecture" />
            </a>
            <button class="hamburger hamburger--emphatic" type="button">
                <div class="hamburger hamburger--spin clear">
                    <div class="hamburger-box">
                        <div class="hamburger-inner"></div>
                    </div>
                </div>
            </button>
            <ul>
                <li class="lang">
                    @{if (ViewBag.lang == "")
                        {
                            ViewBag.lang = "ar";
                        }
                        string yy = ViewBag.lang + ".webp";}
                    <a href="#"><img src="/assets/img/@yy" alt="ar" style="width:26px;height:24px" />@ViewBag.lang <i class="fi flaticon-down-arrow"></i></a>
                    <ul>
                        <li><a  href="#" onclick="chlang('en')"><img alt="en" src="/assets/img/en.webp" /> English</a></li>
                        <li><a  href="#" onclick="chlang('tr')"><img alt="tr" src="/assets/img/tr.webp" /> Türkçe</a></li>
                        <li><a  href="#" onclick="chlang('ar')"><img alt="ar" src="/assets/img/ar.webp" /> العربية</a></li>
                    </ul>
                </li>
            </ul>

            <ul class="offer">
                <li class="offer"><a href="https://wa.me/905539041212">@Resources.Resource.get_offer</a></li>
            </ul>
            @{ if (Session["CurrentLanguage"] != null)
                {
                    if (Session["CurrentLanguage"] == "ar-AE")
                    {
                        <ul class="menu">
                            <li class="nav-item"><a class="nav-link " href="@Url.Action("Index", "Home")">@Resources.Resource.home</a></li>
                            <li class="nav-item  sub-menu "><a class="nav-link " href="#">@Resources.Resource.our_work</a><ul><li class="nav-item"><a class="nav-link " href="@Url.Action("repro", "Home")">@Resources.Resource.resproject</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("copro", "Home")">@Resources.Resource.coproject</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("probycountry", "Home")">@Resources.Resource.projects_as_countries</a></li></ul></li>
                            <li class="nav-item  sub-menu "><a class="nav-link " href="#">@Resources.Resource.gallary</a><ul><li class="nav-item"><a class="nav-link " href="@Url.Action("ourwork", "Home")">@Resources.Resource.project</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("categories", "Home")">@Resources.Resource.category</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("vedpro", "Home")">@Resources.Resource.video</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("g360pro", "Home")">@Resources.Resource.g360_gallery_title</a></li></ul></li>
                            <li class="nav-item"><a class="nav-link " href="@Url.Action("blog", "Home")">@Resources.Resource.blog</a></li>
                            <li class="nav-item  sub-menu "><a class="nav-link " href="#">@Resources.Resource.about_us</a><ul><li class="nav-item"><a class="nav-link " href="@Url.Action("about", "Home")">@Resources.Resource.whysmd</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("hr", "Home")">@Resources.Resource.hr</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("refrencesall", "Home")">@Resources.Resource.all_references</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("faqsall", "Home")">@Resources.Resource.String117</a></li></ul></li>
                            <li class="nav-item"><a class="nav-link " href="@Url.Action("contact", "Home")">@Resources.Resource.contactus</a></li>
                        </ul>}
                    else if (Session["CurrentLanguage"] == "en-En")
                    {
                        <ul class="menu">
                            <li class="nav-item"><a class="nav-link " href="@Url.Action("Index", "Homeen")">@Resources.Resource.home</a></li>
                            <li class="nav-item  sub-menu "><a class="nav-link " href="#">@Resources.Resource.home_our_projects</a><ul><li class="nav-item"><a class="nav-link " href="@Url.Action("repro", "Homeen")">@Resources.Resource.resproject</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("copro", "Homeen")">@Resources.Resource.coproject</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("probycountry", "Homeen")">@Resources.Resource.projects_as_countries</a></li></ul></li>
                            <li class="nav-item  sub-menu "><a class="nav-link " href="#">@Resources.Resource.gallary</a><ul><li class="nav-item"><a class="nav-link " href="@Url.Action("ourwork", "Homeen")">@Resources.Resource.project</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("categories", "Homeen")">@Resources.Resource.category</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("vedpro", "Homeen")">@Resources.Resource.video</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("g360pro", "Homeen")">@Resources.Resource.g360_gallery_title</a></li></ul></li>
                            <li class="nav-item"><a class="nav-link " href="@Url.Action("blog", "Homeen")">@Resources.Resource.blog</a></li>
                            <li class="nav-item  sub-menu "><a class="nav-link " href="#">@Resources.Resource.about_us</a><ul><li class="nav-item"><a class="nav-link " href="@Url.Action("about", "Homeen")">@Resources.Resource.whysmd</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("hr", "Homeen")">@Resources.Resource.hr</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("refrencesall", "Homeen")">@Resources.Resource.all_references</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("faqsall", "Homeen")">@Resources.Resource.String117</a></li></ul></li>
                            <li class="nav-item"><a class="nav-link " href="@Url.Action("contact", "Homeen")">@Resources.Resource.contactus</a></li>
                        </ul>

                    }
                    else
                    {
                        <ul class="menu">
                            <li class="nav-item"><a class="nav-link " href="@Url.Action("Index", "Hometr")">@Resources.Resource.home</a></li>
                            <li class="nav-item  sub-menu "><a class="nav-link " href="#">@Resources.Resource.our_work</a><ul><li class="nav-item"><a class="nav-link " href="@Url.Action("repro", "Hometr")">@Resources.Resource.resproject</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("copro", "Hometr")">@Resources.Resource.coproject</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("probycountry", "Hometr")">@Resources.Resource.projects_as_countries</a></li></ul></li>
                            <li class="nav-item  sub-menu "><a class="nav-link " href="#">@Resources.Resource.gallary</a><ul><li class="nav-item"><a class="nav-link " href="@Url.Action("ourwork", "Hometr")">@Resources.Resource.project</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("categories", "Hometr")">@Resources.Resource.category</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("vedpro", "Hometr")">@Resources.Resource.video</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("g360pro", "Hometr")">@Resources.Resource.g360_gallery_title</a></li></ul></li>
                            <li class="nav-item"><a class="nav-link " href="@Url.Action("blog", "Hometr")">@Resources.Resource.blog</a></li>
                            <li class="nav-item  sub-menu "><a class="nav-link " href="#">@Resources.Resource.about_us</a><ul><li class="nav-item"><a class="nav-link " href="@Url.Action("about", "Hometr")">@Resources.Resource.whysmd</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("hr", "Hometr")">@Resources.Resource.hr</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("refrencesall", "Hometr")">@Resources.Resource.all_references</a></li><li class="nav-item"><a class="nav-link " href="@Url.Action("faqsall", "Hometr")">@Resources.Resource.String117</a></li></ul></li>
                            <li class="nav-item"><a class="nav-link " href="@Url.Action("contact", "Hometr")">@Resources.Resource.contactus</a></li>
                        </ul>

                    }
                } }
        </div>
    </header>

    @RenderBody()

    <footer data-bg="/assets/img/footer-background.webp">
        <div class="footer">
            <div>
                <div>
                    <h6>@Resources.Resource.our_projects</h6>
                    @{ if (Session["CurrentLanguage"] != null)
                        {
                            if (Session["CurrentLanguage"] == "ar-AE")
                            {
                                <ul>
                                    <li><a href="@Url.Action("repro", "Home")">@Resources.Resource.resproject</a></li>
                                    <li><a href="@Url.Action("copro", "Home")">@Resources.Resource.coproject</a></li>
                                    <li><a href="@Url.Action("probycountry", "Home")" class="all">@Resources.Resource.see_all <i class="fi flaticon-next"></i></a></li>

                                </ul>}
                            else if (Session["CurrentLanguage"] == "en-En")
                            {
                                <ul>
                                    <li><a href="@Url.Action("repro", "Homeen")">@Resources.Resource.resproject</a></li>
                                    <li><a href="@Url.Action("copro", "Homeen")">@Resources.Resource.coproject</a></li>
                                    <li><a href="@Url.Action("probycountry", "Homeen")" class="all">@Resources.Resource.see_all <i class="fi flaticon-next"></i></a></li>

                                </ul>

                            }
                            else
                            {
                                <ul>
                                    <li><a href="@Url.Action("repro", "Hometr")">@Resources.Resource.resproject</a></li>
                                    <li><a href="@Url.Action("copro", "Hometr")">@Resources.Resource.coproject</a></li>
                                    <li><a href="@Url.Action("probycountry", "Hometr")" class="all">@Resources.Resource.see_all <i class="fi flaticon-next"></i></a></li>

                                </ul>

                            }
                        } }
                </div>
                <div>
                    <h6>@Resources.Resource.address</h6>
                    <a target="_blank" href="https://g.page/smddecoration?share">
                        <address>Beylizdüzü - Yakuplu Mah. 59. Sk. No:40 Aston A blok Kat:3 D:37</address>
                    </a>
                    <ul>
                        <li><a href="#">İstanbul.Beylizdüzü.VD. <span>7721236806</span></a></li>
                        <!-- <li><a href="#">Beylizdüzü - Yakuplu Mah. 59. Sk. No:40 Aston A blok Kat:3 D:37</a></li> -->
                    </ul>
                </div>
                <div>
                    <a href="contact-us.html"><h6>@Resources.Resource.contactus</h6></a>
                    <ul>
                        <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li><a dir="ltr" href="tel:+90 553 904 12 12">+90 553 904 12 12</a></li>
                        <li><a class="all" target="_blank" href="https://wa.me/905539041212">@Resources.Resource.get_offer <i class="fi flaticon-next"></i></a></li>
                    </ul>
                </div>
                <div>
                    <h6>@Resources.Resource.social_media</h6>
                    <ul class="social">
                        <li><a target="_blank" href="https://www.instagram.com/smddecoration/"><i class="fi flaticon-instagram"></i>Instagram</a></li>
                        <li><a target="_blank" href="https://twitter.com/smddecoration"><i class="fi flaticon-twitter"></i>Twitter</a></li>
                        <li><a target="_blank" href="https://www.facebook.com/smddecoration"><i class="fi flaticon-facebook-logo"></i>Facebook</a></li>
                        <li><a target="_blank" href="https://www.youtube.com/channel/UClLgEoAFdToI0v4VBT548OA?view_as=subscriber"><i class="fab fa-youtube"></i>Youtube</a></li>
                        <li><a target="_blank" href="https://www.tiktok.com/@('@')smddecoration"><i class="fab fa-tiktok"></i>Tiktok</a></li>

                        <li><a target="_blank" href="https://www.behance.net/smddecoration/"><i class="fi flaticon-behance"></i>Behance</a></li>
                        <li><a target="_blank" href="https://tr.pinterest.com/smddecoration/"><i class="fi flaticon-pinterest"></i>Pinterest</a></li>
                        <li><a target="_blank" href="https://www.linkedin.com/company/smddecoration/"><i class="fab fa-linkedin"></i>Linkedin</a></li>


                    </ul>
                </div>
                <div>
                    <h6>@Resources.Resource.important</h6>

                    @{ if (Session["CurrentLanguage"] != null)
                        {
                            if (Session["CurrentLanguage"] == "ar-AE")
                            {
                                <ul>
                                    <li><a href="@Url.Action("categories", "Home")">@Resources.Resource.category</a></li>
                                    <li><a href="@Url.Action("vedpro", "Home")">@Resources.Resource.video</a></li>
                                    <li><a href="@Url.Action("g360pro", "Home")">@Resources.Resource.g360_gallery_title</a></li>
                                    <li><a href="@Url.Action("faqsall", "Home")">@Resources.Resource.String117</a></li>
                                    <li><a href="@Url.Action("hr", "Home")">@Resources.Resource.jobs</a></li>

                                </ul>}
                            else if (Session["CurrentLanguage"] == "en-En")
                            {
                                <ul>
                                    <li><a href="@Url.Action("categories", "Homeen")">@Resources.Resource.category</a></li>
                                    <li><a href="@Url.Action("vedpro", "Homeen")">@Resources.Resource.video</a></li>
                                    <li><a href="@Url.Action("g360pro", "Homeen")">@Resources.Resource.g360_gallery_title</a></li>

                                    <li><a href="@Url.Action("faqsall", "Homeen")">@Resources.Resource.String117</a></li>
                                    <li><a href="@Url.Action("hr", "Homeen")">@Resources.Resource.jobs</a></li>

                                </ul>

                            }
                            else
                            {
                                <ul>
                                    <li><a href="@Url.Action("categories", "Hometr")">@Resources.Resource.category</a></li>
                                    <li><a href="@Url.Action("vedpro", "Hometr")">@Resources.Resource.video</a></li>
                                    <li><a href="@Url.Action("g360pro", "Hometr")">@Resources.Resource.g360_gallery_title</a></li>

                                    <li><a href="@Url.Action("faqsall", "Hometr")">@Resources.Resource.String117</a></li>
                                    <li><a href="@Url.Action("hr", "Hometr")">@Resources.Resource.jobs</a></li>

                                </ul>

                            }
                        } }
                </div>
            </div>
        </div>
        <div class="bottom">
            <div>
                <div class="left">
                    <a href="@Url.Action("privacy", "Home")">Privacy Policy</a>
                    <p> | </p>
                    <a href="#">Terms of Use</a>
                    <p> | </p>
                    <p>&copy; SMD Decoration © <EMAIL> All rights reserved</p>
                </div>
                <div class="right" style="direction:ltr">

                    <p>developed by</p>
                    <a href="https://perfectjobline.com" target="_blank">Perfect Job Line</a>
                </div>
            </div>
        </div>
    </footer>

    <div class="whatsapp">
        <a target="_blank" href="https://wa.me/905539041212">
            <i class="flaticon-whatsapp"></i>
        </a>
    </div>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/gh/fancyapps/fancybox@3.5.7/dist/jquery.fancybox.min.js"></script>

    <script type="text/javascript" src="/assets/js/aos.js"></script>
    <script type="text/javascript" src="/assets/js/main.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/js/all.min.js"></script>
    @RenderSection("scripts", required: false)

    <script>
        $(document).ready(function () {
            $(".fancyboxIframe").fancybox({
                maxWidth: 900,
                maxHeight: 600,
                fitToView: false,
                width: '90%',
                height: '90%',
                autoSize: false,
                closeClick: false,
                openEffect: 'none',
                closeEffect: 'none',
                iframe: {
                    scrolling: 'auto',
                    preload: true
                }
            });
        });
    </script>
    @{ if (Session["CurrentLanguage"] != null)
        {
            if (Session["CurrentLanguage"] == "ar-AE")
            {
                <script>

                    function openNav1() {
                        window.location.replace('/smd-videos/a-minute-idea')
                    }</script>
            }
            else if (Session["CurrentLanguage"] == "en-En")
            {
                <script>

                    function openNav1() {
                        window.location.replace('/en/smd-videos/a-minute-idea')
                    }</script>

            }
            else
            {
                <script>

                    function openNav1() {
                        window.location.replace('/tr/smd-videos/a-minute-idea')
                    }</script>

            }
        } }
    <script>
    function chlang(id) {

        var url1 = '@Url.Action("Chlanar")';
        if (id == 'en') { url1 = '@Url.Action("Chlanen")'; }
        else if (id == 'tr') { url1 = '@Url.Action("Chlantr")'; }



             $.ajax({
                        type: 'POST',
                        url: url1,
                        traditional: true,
                        dataType: 'json',




                        success: function (states) {


                            location.href = states.param2;



                        },
                        error: function (ex) {
                            alert('-' + ex);
                        }
                    });
                    return false;


        }
    </script>

</body>
</html>
