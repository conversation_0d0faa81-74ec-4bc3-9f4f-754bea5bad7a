/*global $, JQuery */

$(document).ready(function () {
    "use strict";
    $(".loading").delay(1500).fadeOut("slow");
    $("header > div > ul li.lang a").click(function () {
        $("header > div > ul li.lang ul").slideToggle();
        $("header > div > ul li.lang > a i").toggleClass("ters");
    });
    $('[dir="ltr"] section.slider').slick({
        dots: true,
        arrows: false,
        autoplay: true,
        autoplaySpeed: 5000,
    });
    $('[dir="rtl"] section.slider').slick({
        dots: true,
        arrows: false,
        autoplay: true,
        autoplaySpeed: 5000,
        rtl: true
    });
    $('[dir="ltr"] section.project-detail > div .slider').slick({
        dots: true,
        autoplay: true,
        autoplaySpeed: 7000,
        prevArrow:'<i class="fi flaticon-next-1 slick-prev"></i>',
        nextArrow:'<i class="fi flaticon-next-1 slick-next"></i>',
    });
    $('[dir="rtl"] section.project-detail > div .slider').slick({
        rtl: true,
        dots: true,
        autoplay: true,
        autoplaySpeed: 7000,
        prevArrow:'<i class="fi flaticon-next-1 slick-prev"></i>',
        nextArrow:'<i class="fi flaticon-next-1 slick-next"></i>',
    });
    $(window).scroll(function() {
        var scroll = $(window).scrollTop();
        if (scroll >= 180) {
            $("header").addClass("small");
        } else {
            $("header").removeClass("small");
        }
    });
    $('[dir="ltr"] section.home-down .references .references-slider').slick({
        arrows: false,
        dots: true,
        slidesToShow: 4,
        slidesToScroll: 2,
        autoplay: true,
        autoplaySpeed: 3000,
        responsive: [
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 3
                }
            },
            {
                breakpoint: 750,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 2,
                    dots: false
                }
            },
            {
                breakpoint: 500,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    dots: false
                }
            },
        ]
    });
    $('[dir="rtl"] section.home-down .references .references-slider').slick({
        arrows: false,
        dots: true,
        slidesToShow: 4,
        slidesToScroll: 2,
        autoplay: true,
        autoplaySpeed: 3000,
        rtl: true,
        responsive: [
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 3
                }
            },
            {
                breakpoint: 750,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 2
                }
            },
            {
                breakpoint: 500,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1
                }
            },
        ]
    });
    $("section.home-down .home-blog .slider").slick({
        arrows: false,
        dots: true,
        slidesToShow: 3,
        slidesToScroll: 3,
        responsive: [
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 3
                }
            },
            {
                breakpoint: 750,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 2
                }
            },
            {
                breakpoint: 500,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1
                }
            },
        ]
    });
    // $("section.humanr ul li > div").click(function () {
    //     if ($(this).next("form").hasClass("active")) {
    //         $(this).next("form").slideUp();
    //         $(this).next("form").removeClass("active");
    //     } else {
    //         $("section.humanr ul li form").slideUp();
    //         $("section.humanr ul li form").removeClass("active");
    //         $(this).next("form").slideDown();
    //         $(this).next("form").addClass("active");
    //     }
    // });
    // $("form .upload").click(function () {
    //     $('form input[type="upload"]').click();
    // });
    $(window).on("load scroll", function() {
        var parallaxElement = $("section.projects > div .background img"),
            parallaxQuantity = parallaxElement.length;
        window.requestAnimationFrame(function() {
            for (var i = 0; i < parallaxQuantity; i++) {
                var currentElement  = parallaxElement.eq(i),
                    windowTop       = $(window).scrollTop(),
                    elementTop      = currentElement.offset().top,
                    elementHeight   = currentElement.height(),
                    viewPortHeight  = window.innerHeight * 0.5 - elementHeight * 0.5,
                    scrolled        = windowTop - elementTop + viewPortHeight;
                currentElement.css({
                    transform: "translate3d(0," + scrolled * -0.25 + "px, 0)"
                });
            }
        });
    });

    $(window).on("load scroll", function () {
       
        var parallaxElement = $("section.gallery > p"),
            parallaxQuantity = parallaxElement.length;
        window.requestAnimationFrame(function () {
           
            for (var i = 0; i < parallaxQuantity; i++) {
               
                var currentElement  = parallaxElement.eq(i),
                    windowTop       = $(window).scrollTop(),
                    elementTop      = currentElement.offset().top,
                    elementHeight   = currentElement.height(),
                    viewPortHeight  = window.innerHeight * 0.5 - elementHeight * 0.5,
                    scrolled        = windowTop - elementTop + viewPortHeight;
                currentElement.css({
                    transform: "translateY(" + scrolled * -0.25 + "px)"
                });
            }
        });
    });

    $(window).on("load scroll", function() {
        var parallaxElement = $("section.home-down .home-blog > p"),
            parallaxQuantity = parallaxElement.length;
       
        window.requestAnimationFrame(function() {
            for (var i = 0; i < parallaxQuantity; i++) {
                var currentElement  = parallaxElement.eq(i),
                    windowTop       = $(window).scrollTop(),
                    elementTop      = currentElement.offset().top,
                    elementHeight   = currentElement.height(),
                    viewPortHeight  = window.innerHeight * 0.5 - elementHeight * 0.5,
                    scrolled        = windowTop - elementTop + viewPortHeight;
                currentElement.css({
                    transform: "translateY(" + scrolled * -0.25 + "px)"
                });
            }
        });
    });

    $(window).on("load scroll", function () {
   
        var parallaxElement = $("section.gallery .background img"),
            parallaxQuantity = parallaxElement.length;
        window.requestAnimationFrame(function () {
            
            for (var i = 0; i < parallaxQuantity; i++) {
                var currentElement  = parallaxElement.eq(i),
                    windowTop       = $(window).scrollTop(),
                    elementTop      = currentElement.offset().top,
                    elementHeight   = currentElement.height(),
                    viewPortHeight  = window.innerHeight * 0.5 - elementHeight * 0.5,
                    scrolled        = windowTop - elementTop + viewPortHeight;
                currentElement.css({
                    transform: "translateY(" + scrolled * 0.5 + "px)"
                });
            }
        });
    });

    $(window).on("load scroll", function() {
        var parallaxElement = $("section.home-down .home-blog .background img"),
            parallaxQuantity = parallaxElement.length;
        window.requestAnimationFrame(function() {
            for (var i = 0; i < parallaxQuantity; i++) {
                var currentElement  = parallaxElement.eq(i),
                    windowTop       = $(window).scrollTop(),
                    elementTop      = currentElement.offset().top,
                    elementHeight   = currentElement.height(),
                    viewPortHeight  = window.innerHeight * 0.5 - elementHeight * 0.5,
                    scrolled        = windowTop - elementTop + viewPortHeight;
                currentElement.css({
                    transform: "translateY(" + scrolled * 0.5 + "px)"
                });
            }
        });
    });

    $(window).on("load scroll", function() {
        var parallaxElement = $("section.about .background img"),
            parallaxQuantity = parallaxElement.length;
        window.requestAnimationFrame(function() {
            for (var i = 0; i < parallaxQuantity; i++) {
                var currentElement  = parallaxElement.eq(i),
                    windowTop       = $(window).scrollTop(),
                    elementTop      = currentElement.offset().top,
                    elementHeight   = currentElement.height(),
                    viewPortHeight  = window.innerHeight * 0.5 - elementHeight * 0.5,
                    scrolled        = windowTop - elementTop + viewPortHeight;
                currentElement.css({
                    transform: "translate3d(0," + scrolled * -0.25 + "px, 0)"
                });
            }
        });
    });

    $(window).on("load scroll", function() {
        var parallaxElement = $("section.whyus .background svg"),
            parallaxQuantity = parallaxElement.length;
        window.requestAnimationFrame(function() {
            for (var i = 0; i < parallaxQuantity; i++) {
                var currentElement  = parallaxElement.eq(i),
                    windowTop       = $(window).scrollTop(),
                    elementTop      = currentElement.offset().top,
                    elementHeight   = currentElement.height(),
                    viewPortHeight  = window.innerHeight * 0.5 - elementHeight * 0.5,
                    scrolled        = windowTop - elementTop + viewPortHeight;
                currentElement.css({
                    transform: "translateY(" + scrolled * -0.25 + "px)"
                });
            }
        });
    });

    $(window).on("load scroll", function() {
        var parallaxElement = $("section.whyus .background div"),
            parallaxQuantity = parallaxElement.length;
        window.requestAnimationFrame(function() {
            for (var i = 0; i < parallaxQuantity; i++) {
                var currentElement  = parallaxElement.eq(i),
                    windowTop       = $(window).scrollTop(),
                    elementTop      = currentElement.offset().top,
                    elementHeight   = currentElement.height(),
                    viewPortHeight  = window.innerHeight * 0.5 - elementHeight * 0.5,
                    scrolled        = windowTop - elementTop + viewPortHeight;
                currentElement.css({
                    transform: "translateY(" + scrolled * 0.4 + "px)"
                });
            }
        });
    });

    $(window).on('resize', function () {
        $("section.project-detail > div .before-after > div #comparison figure div").css("background-size", $("section.project-detail > div .before-after > div").width());
    });

    $("header > div > button").click(function () {
        $(this).toggleClass("is-active");
        $("header").toggleClass("color");
        $("header > div > ul.menu").slideToggle();
    });

    if ($(window).width() < 1200) {
        $("header > div > ul.menu li a").click(function () {
            $(this).next().slideToggle();
        });
    }

});
var divisor = document.getElementById("divisor"),
slider = document.getElementById("slider");
function moveDivisor() {
	divisor.style.width = slider.value+"%";
}
AOS.init({
   easing: 'ease-in-out-sine'
});

function zaman() {
        function getTimeRemaining(endtime) {
            var t = Date.parse(endtime) - Date.parse(new Date()),
                seconds = Math.floor((t / 1000) % 60),
                minutes = Math.floor((t / 1000 / 60) % 60),
                hours = Math.floor((t / (1000 * 60 * 60)) % 24),
                days = Math.floor(t / (1000 * 60 * 60 * 24));
            return {
                'total': t,
                'days': days,
                'hours': hours,
                'minutes': minutes,
                'seconds': seconds
            };
        }
        function initializeClock(id, endtime) {
            var clock = document.getElementById(id),
                daysSpan = clock.querySelector('.days'),
                hoursSpan = clock.querySelector('.hours'),
                minutesSpan = clock.querySelector('.minutes'),
                secondsSpan = clock.querySelector('.seconds');
            function updateClock() {
                var t = getTimeRemaining(endtime);
                daysSpan.innerHTML = t.days;
                hoursSpan.innerHTML = ('0' + t.hours).slice(-2);
                minutesSpan.innerHTML = ('0' + t.minutes).slice(-2);
                secondsSpan.innerHTML = ('0' + t.seconds).slice(-2);
                if (t.total <= 0) {
                    clearInterval(timeinterval);
                }
            }
            updateClock();
            var timeinterval = setInterval(updateClock, 1000);
        }
        initializeClock('clockdiv', deadline);
    }
  //  zaman();
