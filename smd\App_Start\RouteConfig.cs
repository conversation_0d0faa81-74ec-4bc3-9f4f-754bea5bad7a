﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace smd
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");
            routes.MapRoute(name: "poapi",
url: "api/posts",
defaults: new { controller = "poapi", action = "GetPosts" }
);
            routes.MapRoute(name: "feed",
url: "feed/{id}",
defaults: new { controller = "Home", action = "GenerateRSSpost", id = UrlParameter.Optional }
);
            routes.MapRoute(
name: "404",
url: "404",
defaults: new { controller = "Error", action = "Page404" }
); routes.MapRoute(
name: "500",
url: "500",
defaults: new { controller = "Error", action = "Page500" }
); routes.MapRoute(name: "sitemapindex",
url: "sitemapindex.xml",
defaults: new { controller = "Home", action = "SitemapIndex" }
);
routes.MapRoute(name: "sitemap",
url: "sitemap.xml",
defaults: new { controller = "Home", action = "SitemapXml" }
);
            routes.MapRoute(name: "sitemaptags",
url: "alltags/sitemap.xml",
defaults: new { controller = "Home", action = "SitemapXmltag" }
);
            routes.MapRoute(name: "home",
url: "",
defaults: new { controller = "Home", action = "Index" }
);
//            routes.MapRoute(name: "homep",
//url: "{id}",
//defaults: new { controller = "Home", action = "Index", id = UrlParameter.Optional }
//);
            routes.MapRoute(name: "About",
  url: "about-us",
  defaults: new { controller = "Home", action = "About" }
  );
            routes.MapRoute(name: "Contact",
  url: "contact-us",
  defaults: new { controller = "Home", action = "Contact" }
  );
            routes.MapRoute(name: "faqs",
url: "faqs",
defaults: new { controller = "Home", action = "faqsall" }
);

            routes.MapRoute(name: "references",
url: "our-clients",
defaults: new { controller = "Home", action = "refrencesall" }
);
            routes.MapRoute(name: "profile",
url: "our-profile",
defaults: new { controller = "Home", action = "profile" }
); routes.MapRoute(name: "smdvideos",
url: "smd-videos/{id}",
defaults: new { controller = "Home", action = "smdvideos", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "category",
url: "category/{id}",
defaults: new { controller = "Home", action = "category", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "categories",
url: "categories",
defaults: new { controller = "Home", action = "categories" }
);
            routes.MapRoute(name: "blogcat",
url: "category/{id1}/{id}",
defaults: new { controller = "Home", action = "newsdetialsc", id = UrlParameter.Optional, id1 = UrlParameter.Optional }
);
            routes.MapRoute(name: "career",
url: "career",
defaults: new { controller = "Home", action = "hr" }
);
            routes.MapRoute(name: "privacy",
url: "privacy-policy",
defaults: new { controller = "Home", action = "privacy" }
);
            routes.MapRoute(name: "repro",
url: "project-category/residential-projects",
defaults: new { controller = "Home", action = "repro" }
);
            routes.MapRoute(name: "copro",
url: "project-category/commercial-projects",
defaults: new { controller = "Home", action = "copro" }
);
            routes.MapRoute(name: "PrivacyPolicies",
url: "Privacy-Policies",
defaults: new { controller = "Home", action = "PrivacyPolicies" }
);
            routes.MapRoute(name: "probycountry",
url: "projects-as-countries",
defaults: new { controller = "Home", action = "probycountry" }
);
            routes.MapRoute(name: "vedpro",
url: "video-gallery",
defaults: new { controller = "Home", action = "vedpro" }
); 
            routes.MapRoute(name: "g360pro",
url: "360-gallery",
defaults: new { controller = "Home", action = "g360pro" }
);
            routes.MapRoute(name: "ourservise",
    url: "ourservice/{id}",
    defaults: new { controller = "Home", action = "ourservise", id = UrlParameter.Optional }
    );
            routes.MapRoute(name: "newslist",
url: "blog",
defaults: new { controller = "Home", action = "blog" }
);
            routes.MapRoute(name: "plist",
url: "projects",
defaults: new { controller = "Home", action = "ourwork" }
);
            routes.MapRoute(name: "plist1",
url: "Myproject/{action}/{id}",
defaults: new { controller = "projects", action = "index", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "blog",
url: "post/{id}",
defaults: new { controller = "Home", action = "newsdetials", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "Details",
url: "project/{id}",
defaults: new { controller = "Home", action = "projectDetails", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "tags",
url: "tag/{id}",
defaults: new { controller = "Home", action = "tags", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "ensitemap",
url: "en/sitemap.xml",
defaults: new { controller = "Homeen", action = "SitemapXml" }
);
            routes.MapRoute(name: "sitemaptagsen",
url: "en/alltags/sitemap.xml",
defaults: new { controller = "Homeen", action = "SitemapXmltag" }
);



            routes.MapRoute(name: "feeden",
url: "en/feed/{id}",
defaults: new { controller = "Homeen", action = "GenerateRSSpost", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "enHomeen",
url: "en",
defaults: new { controller = "Homeen", action = "Index" }
);
            //            routes.MapRoute(name: "enhomep",
            //url: "en/{id}",
            //defaults: new { controller = "Homeen", action = "Index", id = UrlParameter.Optional }
            //);
            routes.MapRoute(name: "enAbout",
  url: "en/about-us",
  defaults: new { controller = "Homeen", action = "About" }
  );
            routes.MapRoute(name: "enContact",
  url: "en/contact-us",
  defaults: new { controller = "Homeen", action = "Contact" }
  );
            routes.MapRoute(name: "faqsen",
url: "en/faqs",
defaults: new { controller = "Homeen", action = "faqsall" }
);
            routes.MapRoute(name: "tagsen",
url: "en/tag/{id}",
defaults: new { controller = "Homeen", action = "tags", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "enreferences",
url: "en/our-clients",
defaults: new { controller = "Homeen", action = "refrencesall" }
);
            routes.MapRoute(name: "enprofile",
url: "en/our-profile",
defaults: new { controller = "Homeen", action = "profile" }
); routes.MapRoute(name: "ensmdvideos",
url: "en/smd-videos/{id}",
defaults: new { controller = "Homeen", action = "smdvideos", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "encategory",
url: "en/category/{id}",
defaults: new { controller = "Homeen", action = "category", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "encategories",
url: "en/categories",
defaults: new { controller = "Homeen", action = "categories" }
);
            routes.MapRoute(name: "enblogcat",
url: "en/category/{id1}/{id}",
defaults: new { controller = "Homeen", action = "newsdetialsc", id = UrlParameter.Optional, id1 = UrlParameter.Optional }
);
            routes.MapRoute(name: "encareer",
url: "en/career",
defaults: new { controller = "Homeen", action = "hr" }
);
            routes.MapRoute(name: "enrepro",
url: "en/project-category/residential-projects",
defaults: new { controller = "Homeen", action = "repro" }
);
            routes.MapRoute(name: "encopro",
url: "en/project-category/commercial-projects",
defaults: new { controller = "Homeen", action = "copro" }
);
            routes.MapRoute(name: "enPrivacyPolicies",
url: "en/Privacy-Policies",
defaults: new { controller = "Homeen", action = "PrivacyPolicies" }
);
            routes.MapRoute(name: "enprobycountry",
url: "en/projects-as-countries",
defaults: new { controller = "Homeen", action = "probycountry" }
);
            routes.MapRoute(name: "envedpro",
url: "en/video-gallery",
defaults: new { controller = "Homeen", action = "vedpro" }
);
            routes.MapRoute(name: "eng360pro",
url: "en/360-gallery",
defaults: new { controller = "Homeen", action = "g360pro" }
);
            routes.MapRoute(name: "enourservise",
    url: "en/ourservice/{id}",
    defaults: new { controller = "Homeen", action = "ourservise", id = UrlParameter.Optional }
    );
            routes.MapRoute(name: "ennewslist",
url: "en/blog",
defaults: new { controller = "Homeen", action = "blog" }
);
            routes.MapRoute(name: "enplist",
url: "en/projects",
defaults: new { controller = "Homeen", action = "ourwork" }
);
            routes.MapRoute(name: "enplist1",
url: "en/Myproject/{action}/{id}",
defaults: new { controller = "projects", action = "index", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "enblog",
url: "en/post/{id}",
defaults: new { controller = "Homeen", action = "newsdetials", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "enDetails",
url: "en/project/{id}",
defaults: new { controller = "Homeen", action = "projectDetails", id = UrlParameter.Optional }
);

            routes.MapRoute(name: "arHome",
url: "ar",
defaults: new { controller = "Home", action = "Index" }
);
            //            routes.MapRoute(name: "arhomep",
            //url: "ar/{id}",
            //defaults: new { controller = "Home", action = "Index", id = UrlParameter.Optional }
            //);
            routes.MapRoute(name: "arAbout",
  url: "ar/about-us",
  defaults: new { controller = "Home", action = "About" }
  );
            routes.MapRoute(name: "tagstt",
url: "ar/tag/{id}",
defaults: new { controller = "Home", action = "tags", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "arContact",
  url: "ar/contact-us",
  defaults: new { controller = "Home", action = "Contact" }
  );
            routes.MapRoute(name: "faqsar",
url: "ar/faqs",
defaults: new { controller = "Home", action = "faqsall" }
);
            routes.MapRoute(name: "arreferences",
url: "ar/our-clients",
defaults: new { controller = "Home", action = "refrencesall" }
);
            routes.MapRoute(name: "arprofile",
url: "ar/our-profile",
defaults: new { controller = "Home", action = "profile" }
); routes.MapRoute(name: "arsmdvideos",
url: "ar/smd-videos/{id}",
defaults: new { controller = "Home", action = "smdvideos", id = UrlParameter.Optional }

);
            routes.MapRoute(name: "arcategory",
url: "ar/category/{id}",
defaults: new { controller = "Home", action = "category", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "arcategories",
url: "ar/categories",
defaults: new { controller = "Home", action = "categories" }
); 
            routes.MapRoute(name: "arblogcat",
url: "ar/category/{id1}/{id}",
defaults: new { controller = "Home", action = "newsdetialsc", id = UrlParameter.Optional, id1 = UrlParameter.Optional }
);
            routes.MapRoute(name: "arcareer",
url: "ar/career",
defaults: new { controller = "Home", action = "hr" }
);
            routes.MapRoute(name: "arrepro",
url: "ar/project-category/residential-projects",
defaults: new { controller = "Home", action = "repro" }
);
            routes.MapRoute(name: "arcopro",
url: "ar/project-category/commercial-projects",
defaults: new { controller = "Home", action = "copro" }
);
            routes.MapRoute(name: "arPrivacyPolicies",
url: "ar/Privacy-Policies",
defaults: new { controller = "Home", action = "PrivacyPolicies" }
);
            routes.MapRoute(name: "arprobycountry",
url: "ar/projects-as-countries",
defaults: new { controller = "Home", action = "probycountry" }
);
            routes.MapRoute(name: "arvedpro",
url: "ar/video-gallery",
defaults: new { controller = "Home", action = "vedpro" }
);
            routes.MapRoute(name: "arg360pro",
url: "ar/360-gallery",
defaults: new { controller = "Home", action = "g360pro" }
);
            routes.MapRoute(name: "arourservise",
    url: "ar/ourservice/{id}",
    defaults: new { controller = "Home", action = "ourservise", id = UrlParameter.Optional }
    );
            routes.MapRoute(name: "arnewslist",
url: "ar/blog",
defaults: new { controller = "Home", action = "blog" }
);
            routes.MapRoute(name: "arplist",
url: "ar/projects",
defaults: new { controller = "Home", action = "ourwork" }
);
            routes.MapRoute(name: "arplist1",
url: "ar/Myproject/{action}/{id}",
defaults: new { controller = "projects", action = "index", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "arblog",
url: "ar/post/{id}",
defaults: new { controller = "Home", action = "newsdetials", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "arDetails",
url: "ar/project/{id}",
defaults: new { controller = "Home", action = "projectDetails", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "trsitemap",
url: "tr/sitemap.xml",
defaults: new { controller = "Hometr", action = "SitemapXml" }
); routes.MapRoute(name: "sitemaptagstr",
url: "tr/alltags/sitemap.xml",
defaults: new { controller = "Hometr", action = "SitemapXmltag" }
);

            routes.MapRoute(name: "trHometr",
url: "tr",
defaults: new { controller = "Hometr", action = "Index" }
);
            //            routes.MapRoute(name: "trhomep",
            //url: "tr/{id}",
            //defaults: new { controller = "Hometr", action = "Index", id = UrlParameter.Optional }
            //);
            routes.MapRoute(name: "trAbout",
  url: "tr/about-us",
  defaults: new { controller = "Hometr", action = "About" }
  );
            routes.MapRoute(name: "feedtr",
url: "tr/feed/{id}",
defaults: new { controller = "Hometr", action = "GenerateRSSpost", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "trContact",
  url: "tr/contact-us",
  defaults: new { controller = "Hometr", action = "Contact" }
  );
            routes.MapRoute(name: "tagstr",
url: "tr/tag/{id}",
defaults: new { controller = "Hometr", action = "tags", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "faqstr",
url: "tr/faqs",
defaults: new { controller = "Hometr", action = "faqsall" }
);
            routes.MapRoute(name: "trrefertrces",
url: "tr/our-clients",
defaults: new { controller = "Hometr", action = "refrencesall" }
);
            routes.MapRoute(name: "trprofile",
url: "tr/our-profile",
defaults: new { controller = "Hometr", action = "profile" }
); routes.MapRoute(name: "trsmdvideos",
url: "tr/smd-videos/{id}",
defaults: new { controller = "Hometr", action = "smdvideos", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "trcategory",
url: "tr/category/{id}",
defaults: new { controller = "Hometr", action = "category", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "trcategories",
url: "tr/categories",
defaults: new { controller = "Hometr", action = "categories" }
);
            routes.MapRoute(name: "trblogcat",
url: "tr/category/{id1}/{id}",
defaults: new { controller = "Hometr", action = "newsdetialsc", id = UrlParameter.Optional, id1 = UrlParameter.Optional }
);
            routes.MapRoute(name: "trcareer",
url: "tr/career",
defaults: new { controller = "Hometr", action = "hr" }
);
            routes.MapRoute(name: "trrepro",
url: "tr/project-category/residential-projects",
defaults: new { controller = "Hometr", action = "repro" }
);
            routes.MapRoute(name: "trcopro",
url: "tr/project-category/commercial-projects",
defaults: new { controller = "Hometr", action = "copro" }
);
            routes.MapRoute(name: "trPrivacyPolicies",
url: "tr/Privacy-Policies",
defaults: new { controller = "Hometr", action = "PrivacyPolicies" }
);
            routes.MapRoute(name: "trprobycountry",
url: "tr/projects-as-countries",
defaults: new { controller = "Hometr", action = "probycountry" }
);
            routes.MapRoute(name: "trvedpro",
url: "tr/video-gallery",
defaults: new { controller = "Hometr", action = "vedpro" }
);
            routes.MapRoute(name: "trg360pro",
url: "tr/360-gallery",
defaults: new { controller = "Hometr", action = "g360pro" }
);
            routes.MapRoute(name: "trourservise",
    url: "tr/ourservice/{id}",
    defaults: new { controller = "Hometr", action = "ourservise", id = UrlParameter.Optional }
    );
            routes.MapRoute(name: "trnewslist",
url: "tr/blog",
defaults: new { controller = "Hometr", action = "blog" }
);
            routes.MapRoute(name: "trplist",
url: "tr/projects",
defaults: new { controller = "Hometr", action = "ourwork" }
);
            routes.MapRoute(name: "trplist1",
url: "tr/Myproject/{action}/{id}",
defaults: new { controller = "projects", action = "index", id = UrlParameter.Optional }
);

            routes.MapRoute(name: "arplist12",
url: "myfaqs/{action}/{id}",
defaults: new { controller = "faqs", action = "index", id = UrlParameter.Optional });
            routes.MapRoute(name: "enplist12",
url: "en/myfaqs/{action}/{id}",
defaults: new { controller = "faqs", action = "index", id = UrlParameter.Optional });

            routes.MapRoute(name: "trblog",
url: "tr/post/{id}",
defaults: new { controller = "Hometr", action = "newsdetials", id = UrlParameter.Optional }
);
            routes.MapRoute(name: "trDetails",
url: "tr/project/{id}",
defaults: new { controller = "Hometr", action = "projectDetails", id = UrlParameter.Optional }
);
 
            routes.MapRoute(
           name: "LocalizedDefault",
           url: "{lang}/{controller}/{action}/{id}/{id1}/{id2}",
           defaults: new { controller = "Home", action = "Index", id = UrlParameter.Optional, id1 = UrlParameter.Optional, id2 = UrlParameter.Optional },
           constraints: new { lang = "ar-ae|en-us" }
       );
            routes.MapRoute(
                name: "Default",
                url: "{controller}/{action}/{id}/{id1}",
                defaults: new { controller = "Home", action = "Index", id = UrlParameter.Optional, id1 = UrlParameter.Optional }
            );
           
        }
    }
}
