using smd.Models;
using smd.services;
using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Web.Mvc;

namespace smd.Controllers
{
    public class ErrorController : BaseController
    {
        private smddb db = new smddb();
       
       
        public ActionResult PageError()
        {
            string url = System.Web.HttpContext.Current.Request.Url.AbsoluteUri;
            var uri = new Uri(url);
            var domain = GetDomainPart(url);
            var result = "https://" + domain + uri.PathAndQuery;
            string aspxerrorpath = uri.PathAndQuery;
            aspxerrorpath = aspxerrorpath.Replace("/feed/", "");
            aspxerrorpath = aspxerrorpath.Replace("/feed", "");
            string lang = "ar";
            lang = ViewBag.lang;
            if (aspxerrorpath != null)
            {
                var topcourse1 = db.posts.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) && a.lang == "ar").SingleOrDefault();

                if (topcourse1 != null)
                {
                    ViewBag.url = "https://smddecoration.com/post/" + aspxerrorpath.Replace("/", "");
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301

                    Response.StatusCode = 301;
                    Response.AddHeader("Location", "https://smddecoration.com/post/" + aspxerrorpath.Replace("/", "")); // Add the new URL to the Location header
                    Response.End(); // End the response



                }
                var topcourse = db.projects.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) && a.lang == "ar").SingleOrDefault();

                if (topcourse != null)
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/project/" + aspxerrorpath.Replace("/", "");
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response

                }
                if (aspxerrorpath.Contains("اتصل-بنا"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/contact-us";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                //if (aspxerrorpath.Contains("smd-gallery"))
                //{
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //    Response.StatusCode = 301;
                //    ViewBag.url = "https://smddecoration.com/projects";
                //    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //    Response.End(); // End the response
                //}
                if (aspxerrorpath.Contains("smd-videos/contact-us")|| aspxerrorpath.Contains("project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/contact-us";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("en/smd-videos/contact-us") || aspxerrorpath.Contains("en/project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/en/contact-us";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/blog/page"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/blog";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                //if (aspxerrorpath.Contains("/en/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/en/projects/", "");
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    if (aspxerrorpath.Trim() != "")
                //    {

                //        Response.StatusCode = 301;
                //        ViewBag.url = "https://smddecoration.com/en/project/" + aspxerrorpath.Replace("/", "");
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //    else
                //    {
                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://smddecoration.com/en/projects";
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //}
                //else if (aspxerrorpath.Contains("/tr/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/tr/projects/", "");
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    if (aspxerrorpath.Trim() != "")
                //    {
                //        Response.StatusCode = 301;

                //        ViewBag.url = "https://smddecoration.com/tr/project/" + aspxerrorpath.Replace("/", "");
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //    else
                //    {
                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://smddecoration.com/tr/projects";
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //}
                //else if (aspxerrorpath.Contains("/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/projects/", "");
                //    if (aspxerrorpath.Trim() != "")
                //    {
                //        Response.Clear(); // Clear the response to remove any previous content
                //        Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://smddecoration.com/project/" + aspxerrorpath.Replace("/", "");
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End();
                //    }

                //}

                //if (aspxerrorpath.Contains("com/about") && !aspxerrorpath.Contains("com/about-us") && !aspxerrorpath.Contains("en/about-us"))
                //{
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    Response.StatusCode = 301;
                //    ViewBag.st = "1";
                //    ViewBag.url = "https://smddecoration.com/about-us";
                //    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //    Response.End(); // End the response
                //}
                if (aspxerrorpath.Contains("/post/test-post"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/blog";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }

                if (aspxerrorpath.Contains("/wp-admin/post.php?post=4749&action=edit"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/blog";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/en/en/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/en/en/", "");
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/en/" + aspxerrorpath;
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/tr/tr/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/tr/tr/", "");
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/tr/" + aspxerrorpath;
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }


                if (aspxerrorpath == "/tag/smd/" || aspxerrorpath == "/category/image/" || aspxerrorpath == "/projects/contact-us" || aspxerrorpath == "/blog/contact-us" || aspxerrorpath == "/Hometr/addrequests" || aspxerrorpath == "/project-category/contact-us" || aspxerrorpath == "/category/contact-us")
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;
                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/our-profile";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/projects/istanbul-apartment-core" || aspxerrorpath == "/projects/lux-apartment" || aspxerrorpath == "/projects/faster-mobile-servis" || aspxerrorpath == "/projects/fast-food-chiki-wiki" || aspxerrorpath == "/projects/villa-roneela" || aspxerrorpath == "/projects/heavy-classic" || aspxerrorpath == "/projects/baking-colony-restaurant" || aspxerrorpath == "/projects/new-apartment" || aspxerrorpath == "/projects/elit-life" || aspxerrorpath == "/projects/dubai-appartment" || aspxerrorpath == "/projects/istanbul-appartment" || aspxerrorpath == "/projects/stand-abo-shaker" || aspxerrorpath == "/projects/istanbul-marter-apartment" || aspxerrorpath == "/projects/jakamen" || aspxerrorpath == "/projects/kuwait-apartment" || aspxerrorpath == "/projects/villa-qatar" || aspxerrorpath == "/projects/berrly-beauty-salon" || aspxerrorpath == "/projects/coffee-over-82" || aspxerrorpath == "/projects/coffee-3dots")
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/project/" + aspxerrorpath.Replace("/projects/", "");
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/category/الديكور/"|| aspxerrorpath == "/category/الديكور"||aspxerrorpath.Contains("category/الديكور"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/category/Decorations-and-interior-design";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
            }
            Response.TrySkipIisCustomErrors = true;
            return View();
        }
        [Route("404")]
        public ActionResult Page404(string aspxerrorpath)
        {
            
            string lang = "ar";
            lang = ViewBag.lang;
            if (aspxerrorpath != null)
            {
                aspxerrorpath = aspxerrorpath.Replace("/feed/", "");
                aspxerrorpath = aspxerrorpath.Replace("/feed", "");
                var topcourse1 = db.posts.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) && a.lang =="ar").SingleOrDefault();

                if (topcourse1 != null)
                {
                    ViewBag.url = "https://smddecoration.com/post/" + aspxerrorpath.Replace("/", "");
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301

                    Response.StatusCode = 301;
                    Response.AddHeader("Location", "https://smddecoration.com/post/" + aspxerrorpath.Replace("/", "")); // Add the new URL to the Location header
                    Response.End(); // End the response



                }
                var topcourse = db.projects.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) && a.lang == "ar").SingleOrDefault();

                if (topcourse != null)
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/project/" + aspxerrorpath.Replace("/", "");
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response

                }
                if (aspxerrorpath.Contains("اتصل-بنا"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/contact-us";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                //if (aspxerrorpath.Contains("smd-gallery"))
                //{
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //    Response.StatusCode = 301;
                //    ViewBag.url = "https://smddecoration.com/projects";
                //    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //    Response.End(); // End the response
                //}
                if (aspxerrorpath.Contains("smd-videos/contact-us") || aspxerrorpath.Contains("project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/contact-us";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("en/smd-videos/contact-us") || aspxerrorpath.Contains("en/project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/en/contact-us";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/blog/page"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/blog";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/en/projects/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/en/projects/", "");
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    if (aspxerrorpath.Trim() != "")
                    {

                        Response.StatusCode = 301;
                        ViewBag.url = "https://smddecoration.com/en/project/" + aspxerrorpath.Replace("/", "");
                        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                        Response.End(); // End the response
                    }
                    else
                    {
                        Response.StatusCode = 301;
                        ViewBag.st = "1";
                        ViewBag.url = "https://smddecoration.com/en/projects";
                        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                        Response.End(); // End the response
                    }
                }
                else if (aspxerrorpath.Contains("/tr/projects/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/tr/projects/", "");
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    if (aspxerrorpath.Trim() != "")
                    {
                        Response.StatusCode = 301;

                        ViewBag.url = "https://smddecoration.com/tr/project/" + aspxerrorpath.Replace("/", "");
                        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                        Response.End(); // End the response
                    }
                    else
                    {
                        Response.StatusCode = 301;
                        ViewBag.st = "1";
                        ViewBag.url = "https://smddecoration.com/tr/projects";
                        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                        Response.End(); // End the response
                    }
                }
                //else if (aspxerrorpath.Contains("/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/projects/", "");
                //    if (aspxerrorpath.Trim() != "")
                //    {
                //        Response.Clear(); // Clear the response to remove any previous content
                //        Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://smddecoration.com/project/" + aspxerrorpath.Replace("/", "");
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End();
                //    }

                //}

                if (aspxerrorpath.Contains("/about/"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;
                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/about-us";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
         
                if (aspxerrorpath.Contains("/post/test-post"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/blog";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }

                if (aspxerrorpath.Contains("/wp-admin/post.php?post=4749&action=edit"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/blog";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/en/en/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/en/en/", "");
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/en/" + aspxerrorpath;
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/tr/tr/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/tr/tr/", "");
                 



                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/tr/" + aspxerrorpath;
                
                }


                if (aspxerrorpath == "/tag/smd/" || aspxerrorpath == "/category/image/" || aspxerrorpath == "/projects/contact-us" || aspxerrorpath == "/blog/contact-us" || aspxerrorpath == "/Hometr/addrequests" || aspxerrorpath == "/project-category/contact-us"|| aspxerrorpath == "/category/contact-us")
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;
                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/our-profile";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/projects/istanbul-apartment-core" || aspxerrorpath == "/projects/lux-apartment" || aspxerrorpath == "/projects/faster-mobile-servis" || aspxerrorpath == "/projects/fast-food-chiki-wiki" || aspxerrorpath == "/projects/villa-roneela" || aspxerrorpath == "/projects/heavy-classic" || aspxerrorpath == "/projects/baking-colony-restaurant" || aspxerrorpath == "/projects/new-apartment" || aspxerrorpath == "/projects/elit-life" || aspxerrorpath == "/projects/dubai-appartment" || aspxerrorpath == "/projects/istanbul-appartment" || aspxerrorpath == "/projects/stand-abo-shaker" || aspxerrorpath == "/projects/istanbul-marter-apartment" || aspxerrorpath == "/projects/jakamen" || aspxerrorpath == "/projects/kuwait-apartment" || aspxerrorpath == "/projects/villa-qatar" || aspxerrorpath == "/projects/berrly-beauty-salon" || aspxerrorpath == "/projects/coffee-over-82" || aspxerrorpath == "/projects/coffee-3dots")
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/project/" + aspxerrorpath.Replace("/projects/", "");
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/category/الديكور/"|| aspxerrorpath == "/category/الديكور" || aspxerrorpath.Contains("category/الديكور"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/category/Decorations-and-interior-design";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
            }

            // Set proper 404 status code to avoid soft 404 errors
            Response.StatusCode = 404;
            Response.Status = "404 Not Found";
            ViewBag.type = "404";
            Response.TrySkipIisCustomErrors = true;
            return View("PageError");
        }
        string GetDomainPart(string url)
        {
            var doubleSlashesIndex = url.IndexOf("://");
            var start = doubleSlashesIndex != -1 ? doubleSlashesIndex + "://".Length : 0;
            var end = url.IndexOf("/", start);
            if (end == -1)
                end = url.Length;

            string trimmed = url.Substring(start, end - start);
            if (trimmed.StartsWith("www."))
                trimmed = trimmed.Substring("www.".Length);
            return trimmed;
        }
        public ActionResult Page403()
        {
            string url = System.Web.HttpContext.Current.Request.Url.AbsoluteUri;
            var uri = new Uri(url);
            var domain = GetDomainPart(url);
            var result = "https://" + domain + uri.PathAndQuery;
            string aspxerrorpath = uri.PathAndQuery;
            aspxerrorpath = aspxerrorpath.Replace("/feed/", "");
            aspxerrorpath = aspxerrorpath.Replace("/feed", "");

            string lang = "ar";
            lang = ViewBag.lang;
            if (aspxerrorpath != null)
            {
                var topcourse1 = db.posts.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) ).SingleOrDefault();

                if (topcourse1 != null)
                {
                    ViewBag.url = "https://smddecoration.com/post/" + aspxerrorpath.Replace("/", "");
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301

                    Response.StatusCode = 301;
                    Response.AddHeader("Location", "https://smddecoration.com/post/" + aspxerrorpath.Replace("/", "")); // Add the new URL to the Location header
                    Response.End(); // End the response



                }
                var topcourse = db.projects.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) ).SingleOrDefault();

                if (topcourse != null)
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/project/" + aspxerrorpath.Replace("/", "");
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response

                }
                if (aspxerrorpath.Contains("اتصل-بنا"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/contact-us";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                //if (aspxerrorpath.Contains("smd-gallery"))
                //{
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //    Response.StatusCode = 301;
                //    ViewBag.url = "https://smddecoration.com/projects";
                //    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //    Response.End(); // End the response
                //}
                if (aspxerrorpath.Contains("smd-videos/contact-us") || aspxerrorpath.Contains("project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/contact-us";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("en/smd-videos/contact-us") || aspxerrorpath.Contains("en/project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/en/contact-us";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/blog/page"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/blog";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                //if (aspxerrorpath.Contains("/en/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/en/projects/", "");
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    if (aspxerrorpath.Trim() != "")
                //    {

                //        Response.StatusCode = 301;
                //        ViewBag.url = "https://smddecoration.com/en/project/" + aspxerrorpath.Replace("/", "");
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //    else
                //    {
                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://smddecoration.com/en/projects";
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //}
                //else if (aspxerrorpath.Contains("/tr/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/tr/projects/", "");
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    if (aspxerrorpath.Trim() != "")
                //    {
                //        Response.StatusCode = 301;

                //        ViewBag.url = "https://smddecoration.com/tr/project/" + aspxerrorpath.Replace("/", "");
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //    else
                //    {
                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://smddecoration.com/tr/projects";
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //}
                //else if (aspxerrorpath.Contains("/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/projects/", "");
                //    if (aspxerrorpath.Trim() != "")
                //    {
                //        Response.Clear(); // Clear the response to remove any previous content
                //        Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://smddecoration.com/project/" + aspxerrorpath.Replace("/", "");
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End();
                //    }

                //}

                if (aspxerrorpath.Contains("com/about"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;
                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/about-us";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/post/test-post"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/blog";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }

                if (aspxerrorpath.Contains("/wp-admin/post.php?post=4749&action=edit"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/blog";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/en/en/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/en/en/", "");
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/en/" + aspxerrorpath;
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/tr/tr/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/tr/tr/", "");
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/tr/" + aspxerrorpath;
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }


                if (aspxerrorpath == "/tag/smd/" || aspxerrorpath == "/category/image/" || aspxerrorpath == "/projects/contact-us" || aspxerrorpath == "/blog/contact-us" || aspxerrorpath == "/Hometr/addrequests" || aspxerrorpath == "/project-category/contact-us" || aspxerrorpath == "/category/contact-us")
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;
                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/our-profile";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/projects/istanbul-apartment-core" || aspxerrorpath == "/projects/lux-apartment" || aspxerrorpath == "/projects/faster-mobile-servis" || aspxerrorpath == "/projects/fast-food-chiki-wiki" || aspxerrorpath == "/projects/villa-roneela" || aspxerrorpath == "/projects/heavy-classic" || aspxerrorpath == "/projects/baking-colony-restaurant" || aspxerrorpath == "/projects/new-apartment" || aspxerrorpath == "/projects/elit-life" || aspxerrorpath == "/projects/dubai-appartment" || aspxerrorpath == "/projects/istanbul-appartment" || aspxerrorpath == "/projects/stand-abo-shaker" || aspxerrorpath == "/projects/istanbul-marter-apartment" || aspxerrorpath == "/projects/jakamen" || aspxerrorpath == "/projects/kuwait-apartment" || aspxerrorpath == "/projects/villa-qatar" || aspxerrorpath == "/projects/berrly-beauty-salon" || aspxerrorpath == "/projects/coffee-over-82" || aspxerrorpath == "/projects/coffee-3dots")
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/project/" + aspxerrorpath.Replace("/projects/", "");
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/category/الديكور/"|| aspxerrorpath == "/category/الديكور" || aspxerrorpath.Contains("category/الديكور"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/category/Decorations-and-interior-design";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
            }


            Response.StatusCode = 403;
            Response.TrySkipIisCustomErrors = true;
            return View("PageError");
        }
        [Route("500")]
        public ActionResult Page500()
        {
            string url = System.Web.HttpContext.Current.Request.Url.AbsoluteUri;
            var uri = new Uri(url);
            var domain = GetDomainPart(url);
            var result = "https://" + domain + uri.PathAndQuery;
            string aspxerrorpath = uri.PathAndQuery;
            aspxerrorpath = aspxerrorpath.Replace("/feed/", "");
            aspxerrorpath = aspxerrorpath.Replace("/feed", "");

            string lang = "ar";
            lang = ViewBag.lang;
            if (aspxerrorpath != null)
            {
                var topcourse1 = db.posts.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) ).SingleOrDefault();

                if (topcourse1 != null)
                {
                    ViewBag.url = "https://smddecoration.com/post/" + aspxerrorpath.Replace("/", "");
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301

                    Response.StatusCode = 301;
                    Response.AddHeader("Location", "https://smddecoration.com/post/" + aspxerrorpath.Replace("/", "")); // Add the new URL to the Location header
                    Response.End(); // End the response



                }
                var topcourse = db.projects.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) ).SingleOrDefault();

                if (topcourse != null)
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/project/" + aspxerrorpath.Replace("/", "");
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response

                }
                if (aspxerrorpath.Contains("اتصل-بنا"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/contact-us";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                //if (aspxerrorpath.Contains("smd-gallery"))
                //{
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //    Response.StatusCode = 301;
                //    ViewBag.url = "https://smddecoration.com/projects";
                //    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //    Response.End(); // End the response
                //}
                if (aspxerrorpath.Contains("smd-videos/contact-us") || aspxerrorpath.Contains("project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/contact-us";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("en/smd-videos/contact-us") || aspxerrorpath.Contains("en/project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/en/contact-us";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/blog/page"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/blog";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                //if (aspxerrorpath.Contains("/en/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/en/projects/", "");
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    if (aspxerrorpath.Trim() != "")
                //    {

                //        Response.StatusCode = 301;
                //        ViewBag.url = "https://smddecoration.com/en/project/" + aspxerrorpath.Replace("/", "");
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //    else
                //    {
                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://smddecoration.com/en/projects";
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //}
                //else if (aspxerrorpath.Contains("/tr/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/tr/projects/", "");
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    if (aspxerrorpath.Trim() != "")
                //    {
                //        Response.StatusCode = 301;

                //        ViewBag.url = "https://smddecoration.com/tr/project/" + aspxerrorpath.Replace("/", "");
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //    else
                //    {
                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://smddecoration.com/tr/projects";
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //}
                //else if (aspxerrorpath.Contains("/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/projects/", "");
                //    if (aspxerrorpath.Trim() != "")
                //    {
                //        Response.Clear(); // Clear the response to remove any previous content
                //        Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://smddecoration.com/project/" + aspxerrorpath.Replace("/", "");
                //        Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End();
                //    }

                //}

                //if (aspxerrorpath.Contains("com/about") && !aspxerrorpath.Contains("com/about-us") && !aspxerrorpath.Contains("en/about-us"))
                //{
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    Response.StatusCode = 301;
                //    ViewBag.st = "1";
                //    ViewBag.url = "https://smddecoration.com/about-us";
                //    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                //    Response.End(); // End the response
                //}
                if (aspxerrorpath.Contains("/post/test-post"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/blog";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }

                if (aspxerrorpath.Contains("/wp-admin/post.php?post=4749&action=edit"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/blog";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/en/en/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/en/en/", "");
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/en/" + aspxerrorpath;
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/tr/tr/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/tr/tr/", "");
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/tr/" + aspxerrorpath;
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }


                if (aspxerrorpath == "/tag/smd/" || aspxerrorpath == "/category/image/" || aspxerrorpath == "/projects/contact-us" || aspxerrorpath == "/blog/contact-us" || aspxerrorpath == "/Hometr/addrequests" || aspxerrorpath == "/project-category/contact-us" || aspxerrorpath == "/category/contact-us")
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;
                    ViewBag.st = "1";
                    ViewBag.url = "https://smddecoration.com/our-profile";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/projects/istanbul-apartment-core" || aspxerrorpath == "/projects/lux-apartment" || aspxerrorpath == "/projects/faster-mobile-servis" || aspxerrorpath == "/projects/fast-food-chiki-wiki" || aspxerrorpath == "/projects/villa-roneela" || aspxerrorpath == "/projects/heavy-classic" || aspxerrorpath == "/projects/baking-colony-restaurant" || aspxerrorpath == "/projects/new-apartment" || aspxerrorpath == "/projects/elit-life" || aspxerrorpath == "/projects/dubai-appartment" || aspxerrorpath == "/projects/istanbul-appartment" || aspxerrorpath == "/projects/stand-abo-shaker" || aspxerrorpath == "/projects/istanbul-marter-apartment" || aspxerrorpath == "/projects/jakamen" || aspxerrorpath == "/projects/kuwait-apartment" || aspxerrorpath == "/projects/villa-qatar" || aspxerrorpath == "/projects/berrly-beauty-salon" || aspxerrorpath == "/projects/coffee-over-82" || aspxerrorpath == "/projects/coffee-3dots")
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/project/" + aspxerrorpath.Replace("/projects/", "");
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/category/الديكور/"|| aspxerrorpath == "/category/الديكور" || aspxerrorpath.Contains("category/الديكور"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://smddecoration.com/category/Decorations-and-interior-design";
                    Response.AddHeader("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
            }
            Response.StatusCode = 500;
            ViewBag.type = "500";
            Response.TrySkipIisCustomErrors = true;
            return View("PageError");
        }
    }
}